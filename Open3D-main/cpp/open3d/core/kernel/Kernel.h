// ----------------------------------------------------------------------------
// -                        Open3D: www.open3d.org                            -
// ----------------------------------------------------------------------------
// Copyright (c) 2018-2024 www.open3d.org
// SPDX-License-Identifier: MIT
// ----------------------------------------------------------------------------

#pragma once

#include "open3d/core/kernel/BinaryEW.h"
#include "open3d/core/kernel/IndexGetSet.h"
#include "open3d/core/kernel/NonZero.h"
#include "open3d/core/kernel/Reduction.h"
#include "open3d/core/kernel/UnaryEW.h"

namespace open3d {
namespace core {
namespace kernel {

void TestLinalgIntegration();

}  // namespace kernel
}  // namespace core
}  // namespace open3d
