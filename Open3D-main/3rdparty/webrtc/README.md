# Use WebRTC with Open3D

You have two ways to enable WebRTC in Open3D.
- Method 1: Set `-DBUILD_WEBRTC_FROM_SOURCE=OFF` and Open3D will fetch and use
  a pre-compiled WebRTC package.
  - Method 1 Prepare-Phase: Manually compile the WebRTC binaries.
  - Method 1 Consume-Phase: Open3D CMake consumes the pre-compiled WebRTC binaries automatically.
- Method 2: Set `-DBUILD_WEBRTC_FROM_SOURCE=ON`. This is only recommended for advanced users.

```bash
# Main entry points
webrtc_download.cmake # Used by Open3D CMake. Consume pre-compiled WebRTC. (Method 1 Consume-Phase)
webrtc_build.cmake    # Used by Open3D CMake. Build and consume WebRTC.    (Method 2)

# Other files
0001-xxx.patch x3     # Git patch for -DBUILD_WEBRTC_FROM_SOURCE=ON.       (Method 1 Prepare-Phase & Method 2)
CMakeLists.txt        # Used by `webrtc_build.sh` to compile WebRTC.       (Method 1 Prepare-Phase)
Dockerfile.webrtc     # Calls `webrtc_build.sh` to compile WebRTC.         (Method 1 Prepare-Phase)
webrtc_build.sh       # Used by `Dockerfile.webrtc`.                       (Method 1 Prepare-Phase)
webrtc_common.cmake   # Specifies Common WebRTC targets.                   (Method 1 Prepare-Phase)
```

## Method 1

The pre-compiled WebRTC package used in Method 1 is generated by
`docker/Dockerfile.webrtc`. You can build your own pre-compiled
WebRTC package by building this docker container. On macOS or Windows, you'll
need to build WebRTC manually.

## Method 2

1. Download depot_tools, webrtc to following directories:
   ```
   ├── Oepn3D
   ├── depot_tools
   └── webrtc
       ├── .gclient
       └── src
   ```
   Example commands:
   ```bash
   # You're in Open3D
   cd ..
   git clone https://chromium.googlesource.com/chromium/tools/depot_tools.git
   export PATH=$PATH:`realpath depot_tools`
   mkdir webrtc
   cd webrtc
   fetch --no-history webrtc
   cd ../Open3D
   ```
   You may also want to checkout a specific commit for `depot_tools` and webrtc.
   See `Dockerfile.webrtc` and `webrtc_build.sh::download_webrtc_sources()` on
   how to do that.
2. `depot_tools` and `webrtc` need to be compatible, see:
   https://chromium.googlesource.com/chromium/src/+/master/docs/building_old_revisions.md
3. Apply the following patch to enable GLIBCXX_USE_CXX11_ABI selection:
   ```
   0001-build-enable-rtc_use_cxx11_abi-option.patch       # apply to webrtc/src
   0001-src-enable-rtc_use_cxx11_abi-option.patch         # apply to webrtc/src/build
   0001-third_party-enable-rtc_use_cxx11_abi-option.patch # apply to webrtc/src/third_party
   ```
   Note that these patches may or may not be compatible with your custom
   WebRTC commits. You may have to patch them manually.
