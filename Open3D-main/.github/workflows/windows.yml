# FIXME:
# - librealsense upgrade needed to build with MSVC 17
# - MSVC 17 and CUDA 12.4+ error while building CreateCUDAHashBackend.cu (Debug only)
# - PyTorch Ops in debug mode - DLL initialization error while loading open3d_torch_ops.dll
# - CUDA - Draw.exe does not run due to CUDA DLL path issues


name: Windows
permissions: {}

on:
  workflow_dispatch:
    inputs:
      developer_build:
        description: "Set to OFF for Release wheels"
        required: false
        default: "ON"

  push:
    branches:
      - main
  pull_request:
    types: [opened, reopened, synchronize] # Rebuild on new pushes to PR

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

env:
  PIP_VER: "24.3.1"
  CUDA_VERSION: "12.6.0"
  SRC_DIR: "D:\\a\\open3d\\open3d"
  BUILD_DIR: "C:\\Open3D\\build"
  NPROC: 6
  DEVELOPER_BUILD: ${{ github.event.inputs.developer_build || 'ON' }}

jobs:
  windows:
    permissions:
      contents: write  # upload
    runs-on: windows-2022
    strategy:
      fail-fast: false
      matrix:
        BUILD_SHARED_LIBS: [ON, OFF]
        STATIC_RUNTIME: [ON, OFF]
        BUILD_CUDA_MODULE: [ON, OFF]
        CONFIG: [Release, Debug]
        exclude:
          - BUILD_SHARED_LIBS: ON
            STATIC_RUNTIME: ON
          - BUILD_CUDA_MODULE: ON   # FIXME
            CONFIG: Debug
    env:
      BUILD_WEBRTC: ${{ ( matrix.BUILD_SHARED_LIBS == 'OFF' && matrix.STATIC_RUNTIME == 'ON' ) && 'ON' || 'OFF' }}
      BUILD_PYTORCH_OPS: ${{ ( matrix.BUILD_CUDA_MODULE == 'ON' || matrix.CONFIG == 'Debug' ) && 'OFF' || 'ON' }}   # FIXME

    steps:
      - name: Disk space used
        run: Get-PSDrive

      - name: Install CUDA
        if: ${{ matrix.BUILD_CUDA_MODULE == 'ON' }}
        run: |
          # Define variables
          $CUDA_VER_FULL = "${{ env.CUDA_VERSION }}"
          $CUDA_VER_ARR = $CUDA_VER_FULL.Split(".")
          $CUDA_VER = "$($CUDA_VER_ARR[0]).$($CUDA_VER_ARR[1])"
          $CUDA_VER_ID = "$($CUDA_VER_ARR[0])_$($CUDA_VER_ARR[1])"
          # Installer url
          if ( $CUDA_VER_ARR[0] -ge 11 ) {
            $CUDA_URL = "http://developer.download.nvidia.com/compute/cuda/$CUDA_VER_FULL/network_installers/cuda_$($CUDA_VER_FULL)_windows_network.exe"
          } else {
            $CUDA_URL = "http://developer.download.nvidia.com/compute/cuda/$CUDA_VER/Prod/network_installers/cuda_$($CUDA_VER_FULL)_win10_network.exe"
          }
          # Installer arguments
          $CUDA_INSTALL_ARGS = "-s"
          # Required packages
          $CUDA_PACKAGES = "nvcc", "visual_studio_integration", "cublas", "cublas_dev", "cudart", "cusolver", "cusolver_dev", "cusparse", "cusparse_dev", "npp", "npp_dev", "nvtx", "thrust"
          $CUDA_PACKAGES.ForEach({ $CUDA_INSTALL_ARGS += " $($_)_$($CUDA_VER)" })
          # Download and install CUDA
          echo "Downloading CUDA installer from $CUDA_URL"
          Invoke-WebRequest $CUDA_URL -OutFile cuda.exe
          echo "Installing CUDA..."
          Start-Process -Wait -FilePath .\cuda.exe -ArgumentList "$CUDA_INSTALL_ARGS"
          if ( !$? ) {
            exit 1
          }
          # Add CUDA environment variables.
          $CUDA_PATH = "C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v$CUDA_VER"
          echo "CUDA_PATH=$CUDA_PATH" | Out-File -FilePath $Env:GITHUB_ENV -Encoding utf8 -Append
          echo "CUDA_PATH_V$CUDA_VER_ID=$CUDA_PATH" | Out-File -FilePath $Env:GITHUB_ENV -Encoding utf8 -Append
          echo "$CUDA_PATH\bin" | Out-File -FilePath $Env:GITHUB_PATH -Encoding utf8 -Append

      - name: Checkout source code
        uses: actions/checkout@v4

      - name: Set up Python version
        uses: actions/setup-python@v5
        with:
          python-version: '3.10'

      - name: Checkout Open3D-ML
        uses: actions/checkout@v4
        with:
          repository: isl-org/Open3D-ML
          ref: main
          path: open3d_ml

      - name: Install PyTorch requirements
        run: |
          if ( '${{ matrix.BUILD_CUDA_MODULE }}' -eq 'ON' ) {
            # python -m pip install -r open3d_ml/requirements-torch-cuda.txt
            python -m pip install torch torchvision --index-url https://download.pytorch.org/whl/cu124
          } else {
            python -m pip install -r open3d_ml/requirements-torch.txt
          }

      - name: Config
        # Move build directory to C: https://github.com/actions/virtual-environments/issues/1341
        run: |
          $ErrorActionPreference = 'Stop'
          New-Item -Path '${{ env.BUILD_DIR }}' -ItemType Directory
          cd '${{ env.BUILD_DIR }}'
          if ($Env:DEVELOPER_BUILD -ne "OFF") {
            $Env:DEVELOPER_BUILD = "ON"
          }
          cmake --version
          cmake -G "Visual Studio 17 2022" -A x64 `
            -DDEVELOPER_BUILD=$Env:DEVELOPER_BUILD `
            -DBUILD_EXAMPLES=OFF `
            -DCMAKE_INSTALL_PREFIX="C:\Program Files\Open3D" `
            -DBUILD_SHARED_LIBS=${{ matrix.BUILD_SHARED_LIBS }} `
            -DSTATIC_WINDOWS_RUNTIME=${{ matrix.STATIC_RUNTIME }} `
            -DBUILD_COMMON_ISPC_ISAS=ON `
            -DBUILD_LIBREALSENSE=ON `
            -DBUILD_WEBRTC=${{ env.BUILD_WEBRTC }} `
            -DBUILD_UNIT_TESTS=ON `
            -DBUILD_CUDA_MODULE=${{ matrix.BUILD_CUDA_MODULE }} `
            -DBUILD_PYTORCH_OPS=${{ env.BUILD_PYTORCH_OPS }} `
            ${{ env.SRC_DIR }}

      - name: Build
        working-directory: ${{ env.BUILD_DIR }}
        run: |
          $ErrorActionPreference = 'Stop'
          cmake --build . --parallel ${{ env.NPROC }} --config ${{ matrix.CONFIG }} `
            --target build-examples-iteratively
          cmake --build . --parallel ${{ env.NPROC }} --config ${{ matrix.CONFIG }} `
            --target INSTALL

      - name: Package
        working-directory: ${{ env.BUILD_DIR }}
        if: ${{ matrix.BUILD_SHARED_LIBS == 'ON' && matrix.BUILD_CUDA_MODULE == 'OFF' }}
        run: |
          $ErrorActionPreference = 'Stop'
          cmake --build . --parallel ${{ env.NPROC }} --config ${{ matrix.CONFIG }} `
            --target package
          if ("${{ matrix.CONFIG }}" -eq "Debug") {
              Get-ChildItem package/open3d-devel-*.zip | Rename-Item -NewName `
                  {$_.name -Replace '.zip','-dbg.zip'}
          }
          $DEVEL_PKG_NAME=(Get-ChildItem package/open3d-devel-*.zip).Name
          echo "DEVEL_PKG_NAME=$DEVEL_PKG_NAME" | Out-File -FilePath `
            $Env:GITHUB_ENV -Encoding utf8 -Append

      - name: Upload Package
        if: ${{ matrix.BUILD_SHARED_LIBS == 'ON' && matrix.BUILD_CUDA_MODULE == 'OFF' }}
        uses: actions/upload-artifact@v4
        with:
          name: ${{ env.DEVEL_PKG_NAME }}
          path: ${{ env.BUILD_DIR }}/package/${{ env.DEVEL_PKG_NAME }}
          if-no-files-found: error

      - name: Update devel release with package
        if: ${{ github.ref == 'refs/heads/main' && matrix.BUILD_SHARED_LIBS == 'ON' && matrix.BUILD_CUDA_MODULE == 'OFF' }}
        env:
          GH_TOKEN: ${{ github.token }}
        run: |
          gh release upload main-devel ${{ env.BUILD_DIR }}/package/${{ env.DEVEL_PKG_NAME }} --clobber
          gh release view main-devel

      - name: Viewer App
        working-directory: ${{ env.BUILD_DIR }}
        if: ${{ matrix.BUILD_SHARED_LIBS == 'OFF' && matrix.STATIC_RUNTIME == 'ON' && matrix.BUILD_CUDA_MODULE == 'OFF' && matrix.CONFIG == 'Release' }}
        run: |
          $ErrorActionPreference = 'Stop'
          cmake --build . --parallel ${{ env.NPROC }} --config ${{ matrix.CONFIG }} `
            --target Open3DViewer
          cmake --build . --parallel ${{ env.NPROC }} --config ${{ matrix.CONFIG }} `
            --target INSTALL

      - name: Upload Viewer
        if: ${{ matrix.BUILD_SHARED_LIBS == 'OFF' && matrix.STATIC_RUNTIME == 'ON' && matrix.BUILD_CUDA_MODULE == 'OFF' && matrix.CONFIG == 'Release' }}
        uses: actions/upload-artifact@v4
        with:
          name: open3d-app-windows-amd64
          path: C:\Program Files\Open3D\bin\Open3D
          if-no-files-found: error

      - name: Update devel release with viewer
        if: ${{ github.ref == 'refs/heads/main' && matrix.BUILD_SHARED_LIBS == 'OFF' && matrix.STATIC_RUNTIME == 'ON' && matrix.BUILD_CUDA_MODULE == 'OFF' && matrix.CONFIG == 'Release' }}
        env:
          GH_TOKEN: ${{ github.token }}
        run: |
          $Env:OPEN3D_VERSION_FULL = (Select-String -Path "C:/Open3D/build/CMakeCache.txt" -Pattern "OPEN3D_VERSION_FULL").Line.Split('=')[1]
          Compress-Archive -Path "C:/Program Files/Open3D/bin/Open3D" -DestinationPath `
            "open3d-$Env:OPEN3D_VERSION_FULL-app-windows-amd64.zip"
          gh release upload main-devel "open3d-$Env:OPEN3D_VERSION_FULL-app-windows-amd64.zip" --clobber
          gh release view main-devel

      - name: Run C++ unit tests
        if: ${{ matrix.BUILD_CUDA_MODULE == 'OFF' }}
        working-directory: ${{ env.BUILD_DIR }}
        run: |
          echo "Add --gtest_random_seed=SEED to the test command to repeat this test sequence."
          .\bin\${{ matrix.CONFIG }}\tests.exe --gtest_shuffle --gtest_filter=-*ReduceSum64bit2DCase0*:*ReduceSum64bit2DCase3*
      - name: Linking to Open3D
        working-directory: ${{ env.SRC_DIR }}/examples/cmake/open3d-cmake-find-package
        run: |
          $ErrorActionPreference = 'Stop'
          mkdir build
          cd build
          cmake -G "Visual Studio 17 2022" -A x64 `
            -DCMAKE_INSTALL_PREFIX="C:\Program Files\Open3D" `
            -DSTATIC_WINDOWS_RUNTIME=${{ matrix.STATIC_RUNTIME }} `
            ..
          cmake --build . --config ${{ matrix.CONFIG }}
          if ( '${{ matrix.BUILD_CUDA_MODULE }}' -eq 'OFF' ) {    # FIXME
            .\${{ matrix.CONFIG }}\Draw.exe --skip-for-unit-test
          }
          Remove-Item "C:\Program Files\Open3D" -Recurse
      - name: Install Open3D python build requirements
        working-directory: ${{ env.SOURCE_DIR }}
        run: |
          $ErrorActionPreference = 'Stop'
          python -m pip install -U pip==${{ env.PIP_VER }}
          python -m pip install -U -r python/requirements_build.txt
      - name: Install Python package
        working-directory: ${{ env.BUILD_DIR }}
        run: |
          $ErrorActionPreference = 'Stop'
          cmake --build . --config ${{ matrix.CONFIG }} --target install-pip-package
      - name: Import python package
        # If BUILD_SHARED_LIBS == ON, Open3D.dll needs to be copied, which is not recommended for python.
        if: ${{ matrix.BUILD_SHARED_LIBS == 'OFF' && matrix.BUILD_CUDA_MODULE == 'OFF' }}  # FIXME
        run: |
          python -c "import open3d; print('Imported:', open3d)"
          python -c "import open3d; print('CUDA enabled: ', open3d.core.cuda.is_available())"
      - name: Disk space used
        run: Get-PSDrive

  build-wheel:
    name: Build wheel
    permissions:
      contents: write  # upload
    runs-on: windows-2022
    strategy:
      fail-fast: false
      # https://github.community/t/how-to-conditionally-include-exclude-items-in-matrix-eg-based-on-branch/16853/6
      matrix:
        python_version: ['3.10', '3.11', '3.12', '3.13']
        is_main:
          - ${{ github.ref == 'refs/heads/main' }}
        exclude:
          - is_main: false
            python_version: '3.10'
          - is_main: false
            python_version: '3.11'
          - is_main: false
            python_version: '3.12'

    steps:
      - name: Checkout source code
        uses: actions/checkout@v4

      - name: Checkout Open3D-ML
        uses: actions/checkout@v4
        with:
          repository: isl-org/Open3D-ML
          ref: main
          path: open3d_ml

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python_version }}

      - name: Install Python dependencies
        working-directory: ${{ env.SRC_DIR }}
        run: |
          $ErrorActionPreference = 'Stop'
          python -m pip install -U pip==${{ env.PIP_VER }}
          # maturin>1.7.8 breaks dependency y-py (Python 3.12) and pywinpty (Python 3.8) wheel building
          # https://github.com/MichaIng/DietPi/issues/7329#issuecomment-2565130837
          python -m pip install maturin==1.7.8
          python -m pip install -U -r python/requirements.txt `
            -r python/requirements_build.txt `
            -r python/requirements_jupyter_build.txt `
            -r open3d_ml/requirements-torch.txt


      - name: Config
        run: |
          $ErrorActionPreference = 'Stop'
          New-Item -Path ${{ env.BUILD_DIR }} -ItemType Directory
          cd ${{ env.BUILD_DIR }}
          if ($Env:DEVELOPER_BUILD -ne "OFF") {
            $Env:DEVELOPER_BUILD = "ON"
          }
          cmake -G "Visual Studio 17 2022" -A x64 `
            -DCMAKE_INSTALL_PREFIX="C:\Program Files\Open3D" `
            -DDEVELOPER_BUILD="$Env:DEVELOPER_BUILD" `
            -DBUILD_SHARED_LIBS=OFF `
            -DSTATIC_WINDOWS_RUNTIME=ON `
            -DBUILD_COMMON_ISPC_ISAS=ON `
            -DBUILD_AZURE_KINECT=ON `
            -DBUILD_LIBREALSENSE=ON `
            -DBUILD_WEBRTC=ON `
            -DBUILD_JUPYTER_EXTENSION=ON `
            -DBUILD_PYTORCH_OPS=ON `
            ${{ env.SRC_DIR }}

      - name: Build Python package
        working-directory: ${{ env.BUILD_DIR }}
        run: |
          $ErrorActionPreference = 'Stop'
          cmake --build . --parallel ${{ env.NPROC }} --config Release --target pip-package
          $PIP_PKG_NAME=(Get-ChildItem lib/python_package/pip_package/open3d*.whl).Name
          echo "PIP_PKG_NAME=$PIP_PKG_NAME"  | Out-File -FilePath $Env:GITHUB_ENV -Encoding utf8 -Append

      - name: Upload wheel
        uses: actions/upload-artifact@v4
        with:
          name: ${{ env.PIP_PKG_NAME }}
          path: ${{ env.BUILD_DIR }}/lib/python_package/pip_package/${{ env.PIP_PKG_NAME }}
          if-no-files-found: error

      - name: Update devel release with wheel
        if: ${{ github.ref == 'refs/heads/main' }}
        env:
          GH_TOKEN: ${{ github.token }}
        run: |
          gh release upload main-devel ${{ env.BUILD_DIR }}/lib/python_package/pip_package/${{ env.PIP_PKG_NAME }} --clobber
          gh release view main-devel

  test-wheel:
    name: Test wheel
    permissions:
      contents: read
    runs-on: windows-2022
    needs: [build-wheel]
    strategy:
      fail-fast: false
      matrix:
        python_version: ['3.10', '3.11', '3.12', '3.13']
        is_main:
          - ${{ github.ref == 'refs/heads/main' }}
        exclude:
          - is_main: false
            python_version: '3.10'
          - is_main: false
            python_version: '3.11'
          - is_main: false
            python_version: '3.12'

    steps:
      - name: Checkout source code
        uses: actions/checkout@v4

      - name: Checkout Open3D-ML
        uses: actions/checkout@v4
        with:
          repository: isl-org/Open3D-ML
          ref: main
          path: open3d_ml

      - name: Download wheels
        uses: actions/download-artifact@v4
        with:
          pattern: open3d-*win*.whl
          merge-multiple: true

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python_version }}

      - name: Test Python package
        run: |
          $ErrorActionPreference = 'Stop'
          python -V
          python -m venv open3d_test_venv
          open3d_test_venv\Scripts\Activate.ps1
          python -m pip install -U pip==${{ env.PIP_VER }}
          python -m pip install -U -r python/requirements_build.txt `
            -r python/requirements_test.txt `
            -r open3d_ml/requirements-torch.txt
          $py_tag=(python -c "import sys; print(f'cp{sys.version_info.major}{sys.version_info.minor}')")
          if (Test-Path -Path "pip_package") {
            $PIP_PKG_NAME=(Get-ChildItem pip_package\open3d*-$py_tag-*.whl).Name
          } else {
            $PIP_PKG_NAME=(Get-ChildItem open3d*-$py_tag-*.whl).Name
          }
          echo "Installing Open3D wheel $PIP_PKG_NAME in virtual environment..."
          python -m pip install "$PIP_PKG_NAME"
          python -c "import open3d; print('Imported:', open3d)"
          python -c "import open3d; print('CUDA enabled: ', open3d.core.cuda.is_available())"
          deactivate

      - name: Run Python unit tests
        run: |
          $ErrorActionPreference = 'Stop'
          open3d_test_venv\Scripts\Activate.ps1
          echo "Running Open3D python tests..."
          echo "Add --randomly-seed=SEED to the test command to reproduce test order."
          python -m pytest python/test/
          deactivate
