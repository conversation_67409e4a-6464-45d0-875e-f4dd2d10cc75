name: Ubuntu
permissions: {}

on:
  workflow_dispatch:
    inputs:
      developer_build:
        description: 'Set to OFF for Release wheels'
        required: false
        default: 'ON'
  push:
    branches:
      - main
  pull_request:
    types: [opened, reopened, synchronize]

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  ubuntu:
    permissions:
      contents: write  # Release upload
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        BUILD_SHARED_LIBS: [ON, OFF]
    env:
      BUILD_SHARED_LIBS: ${{ matrix.BUILD_SHARED_LIBS }}
      MLOPS: ${{ matrix.BUILD_SHARED_LIBS }}   # Build Torch and TF ops if shared libs
      BUILD_CUDA_MODULE: OFF
      DEVELOPER_BUILD: ${{ github.event.inputs.developer_build || 'ON' }}
      OPEN3D_CPU_RENDERING: true
    steps:
      - name: Checkout source code
        uses: actions/checkout@v4
      - name: Maximize build space
        run: |
          source util/ci_utils.sh
          maximize_ubuntu_github_actions_build_space
      - name: Docker build
        run: |
          if [ "${{ matrix.BUILD_SHARED_LIBS }}" = "OFF" ] && [ "${{ env.MLOPS }}" = "OFF" ] && [ "${{ env.DEVELOPER_BUILD }}" = "ON" ]; then
            docker/docker_build.sh cpu-static
          elif [ "${{ matrix.BUILD_SHARED_LIBS }}" = "OFF" ] && [ "${{ env.MLOPS }}" = "OFF" ] && [ "${{ env.DEVELOPER_BUILD }}" = "OFF" ]; then
            docker/docker_build.sh cpu-static-release
          elif [ "${{ matrix.BUILD_SHARED_LIBS }}" = "ON"  ] && [ "${{ env.MLOPS }}" = "ON"  ] && [ "${{ env.DEVELOPER_BUILD }}" = "ON"  ]; then
            docker/docker_build.sh cpu-shared-ml
          elif [ "${{ matrix.BUILD_SHARED_LIBS }}" = "ON"  ] && [ "${{ env.MLOPS }}" = "ON"  ] && [ "${{ env.DEVELOPER_BUILD }}" = "OFF" ]; then
            docker/docker_build.sh cpu-shared-ml-release
          fi
      - name: Docker test
        run: |
          if [ "${{ matrix.BUILD_SHARED_LIBS }}" = "OFF" ] && [ "${{ env.MLOPS }}" = "OFF" ] && [ "${{ env.DEVELOPER_BUILD }}" = "ON" ]; then
            docker/docker_test.sh cpu-static
          elif [ "${{ matrix.BUILD_SHARED_LIBS }}" = "OFF" ] && [ "${{ env.MLOPS }}" = "OFF" ] && [ "${{ env.DEVELOPER_BUILD }}" = "OFF" ]; then
            docker/docker_test.sh cpu-static-release
          elif [ "${{ matrix.BUILD_SHARED_LIBS }}" = "ON"  ] && [ "${{ env.MLOPS }}" = "ON"  ] && [ "${{ env.DEVELOPER_BUILD }}" = "ON"  ]; then
            docker/docker_test.sh cpu-shared-ml
          elif [ "${{ matrix.BUILD_SHARED_LIBS }}" = "ON"  ] && [ "${{ env.MLOPS }}" = "ON"  ] && [ "${{ env.DEVELOPER_BUILD }}" = "OFF" ]; then
            docker/docker_test.sh cpu-shared-ml-release
          fi
      - name: Upload package to GitHub artifacts
        if: ${{ env.BUILD_SHARED_LIBS == 'ON' }}
        uses: actions/upload-artifact@v4
        with:
          name: open3d-devel-linux-x86_64
          path: open3d-devel-*.tar.xz
          if-no-files-found: error
      - name: Upload viewer to GitHub artifacts
        if: ${{ env.BUILD_SHARED_LIBS == 'OFF' }}
        uses: actions/upload-artifact@v4
        with:
          name: open3d-viewer-Linux
          path: open3d-viewer-*-Linux.deb
          if-no-files-found: error
      - name: Update devel release
        if: ${{ github.ref == 'refs/heads/main' }}
        env:
          GH_TOKEN: ${{ github.token }}
        run: |
          if [ ${BUILD_SHARED_LIBS} == 'ON' ] ; then
            gh release upload main-devel open3d-devel-*.tar.xz --clobber
          else
            gh release upload main-devel open3d-viewer-*-Linux.deb --clobber
          fi
          gh release view main-devel
