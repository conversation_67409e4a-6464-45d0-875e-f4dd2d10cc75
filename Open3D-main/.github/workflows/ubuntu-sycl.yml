name: Ubuntu SYCL
permissions: {}

on:
  workflow_dispatch:
    inputs:
      developer_build:
        description: 'Set to OFF for Release wheels'
        required: false
        default: 'ON'
  push:
    branches:
      - main
  pull_request:
    types: [opened, reopened, synchronize]

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

env:
  GCE_CLI_GHA_VERSION: "416.0.0"
  DEVELOPER_BUILD: ${{ github.event.inputs.developer_build || 'ON' }}

jobs:
  ubuntu-sycl:
    permissions:
      contents: write  # Release upload
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        BUILD_SHARED_LIBS: [ON, OFF]
    steps:
      - name: Checkout source code
        uses: actions/checkout@v4
      - name: Maximize build space
        run: |
          source util/ci_utils.sh
          maximize_ubuntu_github_actions_build_space
      - name: Docker build
        run: |
          if [ "${{ matrix.BUILD_SHARED_LIBS }}" = "ON" ]; then
            docker/docker_build.sh sycl-shared
          else
            docker/docker_build.sh sycl-static
          fi
      - name: Docker test
        run: |
          du -hs $PWD
          if [ "${{ matrix.BUILD_SHARED_LIBS }}" = "ON" ]; then
            docker/docker_test.sh sycl-shared
          else
            docker/docker_test.sh sycl-static
          fi

      - name: Upload Python wheel and C++ binary package to GitHub artifacts
        if: ${{ matrix.BUILD_SHARED_LIBS == 'ON' }}
        uses: actions/upload-artifact@v4
        with:
          name: open3d-sycl-linux-wheel-and-binary
          path: |
            open3d*.whl
            open3d-devel-*.tar.xz
          if-no-files-found: error
      - name: Update devel release
        if: ${{ github.ref == 'refs/heads/main' && matrix.BUILD_SHARED_LIBS == 'ON' }}
        env:
          GH_TOKEN: ${{ github.token }}
        run: |
          if [ ${{ matrix.BUILD_SHARED_LIBS }} == 'ON' ] ; then
            gh release upload main-devel open3d-*.whl --clobber
            gh release upload main-devel open3d-devel-*.tar.xz --clobber
          fi
          gh release view main-devel

      - name: GCloud CLI auth
        if: ${{ github.ref == 'refs/heads/main' }}
        uses: 'google-github-actions/auth@v2'
        with:
          project_id: ${{ secrets.GCE_PROJECT }}
          credentials_json: '${{ secrets.GCE_SA_KEY_GPU_CI }}'
      - name: GCloud CLI setup
        if: ${{ github.ref == 'refs/heads/main' }}
        uses: google-github-actions/setup-gcloud@v2
        with:
          version: ${{ env.GCE_CLI_GHA_VERSION }}
          project_id: ${{ secrets.GCE_PROJECT }}
      - name: Upload ccache to GCS
        if: ${{ github.ref == 'refs/heads/main' }}
        run: |
          gsutil cp ${GITHUB_WORKSPACE}/open3d-ci-sycl.tar.gz gs://open3d-ci-cache/ || true
