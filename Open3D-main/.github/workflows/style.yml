name: Style Check
permissions: {}

on:
  workflow_dispatch:
  push:
    branches:
      - main
  pull_request:
    types: [opened, reopened, synchronize] # Rebuild on new pushes to PR

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  style-check:
    permissions:
      contents: read
    runs-on: ubuntu-latest
    steps:
      - name: Checkout source code
        uses: actions/checkout@v4
      - name: Set up Python version
        uses: actions/setup-python@v5
        with:
          python-version: '3.12'
      - name: Install dependencies
        run: |
          pip install -U -r python/requirements_style.txt
      - name: Run style check
        run: |
          python util/check_style.py
