name: Ubuntu Wheel
permissions: {}

on:
  workflow_dispatch:
    inputs:
      developer_build:
        description: 'Set to OFF for Release wheels'
        required: false
        default: 'ON'
  push:
    branches:
      - main
  pull_request:
    types: [opened, reopened, synchronize]

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

env:
  GCE_CLI_GHA_VERSION: '416.0.0'      # Fixed to avoid dependency on API changes
  BUILD_CUDA_MODULE: 'ON'
  BUILD_PYTORCH_OPS: 'ON'
  BUILD_TENSORFLOW_OPS: 'OFF'   # Turn ON when cxx11_abi is same for TF and PyTorch

jobs:
  build-wheel:
    permissions:
      contents: write  # Release upload
    name: Build wheel
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        python_version: ['3.10', '3.11', '3.12', '3.13']
        is_main:
          - ${{ github.ref == 'refs/heads/main' }}
        exclude:
          - is_main: false
            python_version: '3.10'
          - is_main: false
            python_version: '3.11'
          - is_main: false
            python_version: '3.12'
    env:
      DEVELOPER_BUILD: ${{ github.event.inputs.developer_build || 'ON' }}
      PYTHON_VERSION: ${{ matrix.python_version }}
      CCACHE_TAR_NAME: open3d-ubuntu-2004-cuda-ci-ccache
      OPEN3D_CPU_RENDERING: true
    steps:
      - name: Checkout source code
        uses: actions/checkout@v4
      - name: Maximize build space
        run: |
          source util/ci_utils.sh
          maximize_ubuntu_github_actions_build_space
      # Be verbose and explicit here such that a developer can directly copy the
      # `docker/docker_build.sh xxx` command to execute locally.
      - name: Docker build
        run: |
          if [ "${{ env.PYTHON_VERSION }}" = "3.10" ] && [ "${{ env.DEVELOPER_BUILD }}" = "ON"  ]; then
            docker/docker_build.sh cuda_wheel_py310_dev
          elif [ "${{ env.PYTHON_VERSION }}" = "3.11" ] && [ "${{ env.DEVELOPER_BUILD }}" = "ON"  ]; then
            docker/docker_build.sh cuda_wheel_py311_dev
          elif [ "${{ env.PYTHON_VERSION }}" = "3.12" ] && [ "${{ env.DEVELOPER_BUILD }}" = "ON"  ]; then
            docker/docker_build.sh cuda_wheel_py312_dev
          elif [ "${{ env.PYTHON_VERSION }}" = "3.13" ] && [ "${{ env.DEVELOPER_BUILD }}" = "ON"  ]; then
            docker/docker_build.sh cuda_wheel_py313_dev
          elif [ "${{ env.PYTHON_VERSION }}" = "3.9" ] && [ "${{ env.DEVELOPER_BUILD }}" = "OFF" ]; then
            docker/docker_build.sh cuda_wheel_py39
          elif [ "${{ env.PYTHON_VERSION }}" = "3.10" ] && [ "${{ env.DEVELOPER_BUILD }}" = "OFF" ]; then
            docker/docker_build.sh cuda_wheel_py310
          elif [ "${{ env.PYTHON_VERSION }}" = "3.11" ] && [ "${{ env.DEVELOPER_BUILD }}" = "OFF" ]; then
            docker/docker_build.sh cuda_wheel_py311
          elif [ "${{ env.PYTHON_VERSION }}" = "3.12" ] && [ "${{ env.DEVELOPER_BUILD }}" = "OFF" ]; then
            docker/docker_build.sh cuda_wheel_py312
          elif [ "${{ env.PYTHON_VERSION }}" = "3.13" ] && [ "${{ env.DEVELOPER_BUILD }}" = "OFF" ]; then
            docker/docker_build.sh cuda_wheel_py313
          fi
          PIP_PKG_NAME="$(basename ${GITHUB_WORKSPACE}/open3d-[0-9]*.whl)"
          PIP_CPU_PKG_NAME="$(basename ${GITHUB_WORKSPACE}/open3d_cpu*.whl)"
          echo "PIP_PKG_NAME=$PIP_PKG_NAME" >> $GITHUB_ENV
          echo "PIP_CPU_PKG_NAME=$PIP_CPU_PKG_NAME" >> $GITHUB_ENV
      - name: Upload wheel to GitHub artifacts
        uses: actions/upload-artifact@v4
        with:
          name: ${{ env.PIP_PKG_NAME }}
          path: |
            ${{ env.PIP_PKG_NAME }}
            ${{ env.PIP_CPU_PKG_NAME }}
          if-no-files-found: error
      - name: GCloud CLI auth
        if: ${{ github.ref == 'refs/heads/main' }}
        uses: 'google-github-actions/auth@v2'
        with:
          project_id: ${{ secrets.GCE_PROJECT }}
          credentials_json: '${{ secrets.GCE_SA_KEY_GPU_CI }}'
      - name: GCloud CLI setup
        if: ${{ github.ref == 'refs/heads/main' }}
        uses: google-github-actions/setup-gcloud@v2
        with:
          version: ${{ env.GCE_CLI_GHA_VERSION }}
          project_id: ${{ secrets.GCE_PROJECT }}
      - name: Upload ccache to GCS
        if: ${{ github.ref == 'refs/heads/main' }}
        run: |
          gsutil cp ${GITHUB_WORKSPACE}/${{ env.CCACHE_TAR_NAME }}.tar.xz gs://open3d-ci-cache/
      - name: Update devel release
        if: ${{ github.ref == 'refs/heads/main' }}
        env:
          GH_TOKEN: ${{ github.token }}
        run: |
          gh release upload main-devel ${GITHUB_WORKSPACE}/${{ env.PIP_PKG_NAME }} \
          ${GITHUB_WORKSPACE}/${{ env.PIP_CPU_PKG_NAME }} --clobber
          gh release view main-devel

  test-wheel-cpu:
    name: Test wheel CPU
    permissions:
      contents: read
    runs-on: ubuntu-22.04
    needs: [build-wheel]
    strategy:
      fail-fast: false
      matrix:
        python_version: ['3.10', '3.11', '3.12', '3.13']
        is_main:
          - ${{ github.ref == 'refs/heads/main' }}
        exclude:
          - is_main: false
            python_version: '3.10'
          - is_main: false
            python_version: '3.11'
          - is_main: false
            python_version: '3.12'
    env:
      OPEN3D_ML_ROOT: ${{ github.workspace }}/Open3D-ML
    steps:
      - name: Checkout source code
        uses: actions/checkout@v4
      - name: Maximize build space
        run: |
          source util/ci_utils.sh
          maximize_ubuntu_github_actions_build_space
      - name: Checkout Open3D-ML source code
        uses: actions/checkout@v4
        with:
          repository: isl-org/Open3D-ML
          ref: main
          path: ${{ env.OPEN3D_ML_ROOT }}
      - name: Download wheels
        uses: actions/download-artifact@v4
        with:
          pattern: open3d*-manylinux*.whl
          merge-multiple: true
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python_version }}
      - name: Test Python package
        run: |
          python -V
          source util/ci_utils.sh
          pi_tag=$(python -c "import sys; print(f'cp{sys.version_info.major}{sys.version_info.minor}')")
          test_wheel open3d-[0-9]*-"$pi_tag"-*.whl
      - name: Run Python unit tests
        run: |
          source util/ci_utils.sh
          echo "Running Open3D python tests..."
          run_python_tests
      - name: Test Python package (CPU)
        env:
          BUILD_CUDA_MODULE: OFF
        run: |
          python -V
          source util/ci_utils.sh
          pi_tag=$(python -c "import sys; print(f'cp{sys.version_info.major}{sys.version_info.minor}')")
          test_wheel open3d_cpu*-"$pi_tag"-*.whl
      - name: Run Python unit tests (CPU)
        run: |
          source util/ci_utils.sh
          echo "Running Open3D python tests (CPU wheel)..."
          run_python_tests
