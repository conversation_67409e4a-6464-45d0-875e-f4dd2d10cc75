name: Ubuntu OpenBLAS
permissions: {}

on:
  workflow_dispatch:
  push:
    branches:
      - main
  pull_request:
    types: [opened, reopened, synchronize]

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  openblas-amd64:
    permissions:
      contents: read
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
    steps:
      - name: Checkout source code
        uses: actions/checkout@v4
      - name: Maximize build space
        run: |
          source util/ci_utils.sh
          maximize_ubuntu_github_actions_build_space

      - name: Docker build
        run: docker/docker_build.sh openblas-amd64-py312-dev

      - name: Docker test
        run: docker/docker_test.sh openblas-amd64-py312-dev

  openblas-arm64:
    permissions:
      contents: read
    runs-on: ubuntu-24.04-arm   # latest
    strategy:
      fail-fast: false
      matrix:
        python_version: ['3.10', '3.11', '3.12', '3.13']
        is_main:
          - ${{ github.ref == 'refs/heads/main' }}
        exclude:
          - is_main: false
            python_version: '3.10'
          - is_main: false
            python_version: '3.11'
          - is_main: false
            python_version: '3.12'
    env:
      DEVELOPER_BUILD: ${{ github.event.inputs.developer_build || 'ON' }}
      PYTHON_VERSION: ${{ matrix.python_version }}
      OPEN3D_CPU_RENDERING: true
    steps:
      - name: Checkout source code
        uses: actions/checkout@v4
      - name: Maximize build space
        run: |
          source util/ci_utils.sh
          maximize_ubuntu_github_actions_build_space

      - name: Compute Docker tag for this matrix entry
        run: |
          # Strip the dot: 3.8 ➜ 38, 3.10 ➜ 310 …
          PY_NO_DOT="${PYTHON_VERSION//./}"
          # Add “-dev” only when requested
          DEV_SUFFIX=$([ "${DEVELOPER_BUILD}" = "ON" ] && echo "-dev")
          echo "DOCKER_TAG=openblas-arm64-py${PY_NO_DOT}${DEV_SUFFIX}" >> $GITHUB_ENV

      - name: Docker build
        run: |
          docker/docker_build.sh "${DOCKER_TAG}"

      - name: Docker test
        run: docker/docker_test.sh "${DOCKER_TAG}"

      - name: Upload wheel to GitHub artifacts
        uses: actions/upload-artifact@v4
        with:
          path: open3d-*.whl
          if-no-files-found: error

      - name: Update devel release
        if: ${{ github.ref == 'refs/heads/main' }}
        env:
          GH_TOKEN: ${{ github.token }}
        run: |
          gh release upload main-devel open3d-*.whl --clobber
          gh release view main-devel
