name: Documentation
permissions: {}

on:
  workflow_dispatch:
    inputs:
      developer_build:
        description: 'Set to OFF for Release documentation'
        required: false
        default: 'ON'
  push:
    branches:
      - main
  pull_request:
    types: [opened, reopened, synchronize]

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  headless-docs:   # Build headless and docs
    permissions:
      contents: write  # Artifact upload and release upload
    runs-on: ubuntu-latest
    env:
      DEVELOPER_BUILD: ${{ github.event.inputs.developer_build || 'ON' }}
    steps:
      - name: Checkout Open3D source code
        uses: actions/checkout@v4

      - name: Maximize build space
        run: |
          source util/ci_utils.sh
          maximize_ubuntu_github_actions_build_space
        
    # Pinning to 22.04 instead of ubuntu-latest until 24.04 is supported
    # (see https://github.com/isl-org/Open3D/pull/7105)
      - name: Docker build
        run: |
          docker build --build-arg BASE_IMAGE=ubuntu:22.04 \
          --build-arg DEVELOPER_BUILD=${DEVELOPER_BUILD} \
          -t open3d-ci:docs -f docker/Dockerfile.docs .

          docker run -v "${GITHUB_WORKSPACE}:/opt/mount" --rm open3d-ci:docs \
              bash -c "cp /root/Open3D/open3d-*-docs.tar.gz /opt/mount \
                    && chown $(id -u):$(id -g) /opt/mount/open3d-*-docs.tar.gz"

          # Rename from Github PR branch SHA to original branch SHA, if needed.
          mv open3d-*-docs.tar.gz open3d-${GITHUB_SHA}-docs.tar.gz

      - name: Upload docs
        uses: actions/upload-artifact@v4
        with:
          name: open3d-${{ github.sha }}-docs
          path: open3d-${{ github.sha }}-docs.tar.gz
          if-no-files-found: error
          compression-level: 0 # no compression

      - name: Update devel release
        if: ${{ github.ref == 'refs/heads/main' }}
        env:
          GH_TOKEN: ${{ github.token }}
        run: |
          gh release upload main-devel open3d-${{ github.sha }}-docs.tar.gz --clobber
          gh release view main-devel
