<!--- Provide a general summary of your changes in the Title above -->

## Type

<!--- Select with 'x' and link to a related issue. What types of changes does your code introduce? -->

-   [ ] Bug fix (non-breaking change which fixes an issue): Fixes #
-   [ ] New feature (non-breaking change which adds functionality). Resolves #
-   [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected) Resolves #

## Motivation and Context

<!--- Why is this change required? What problem does it solve? -->

## Checklist:

<!--- Go over all the following points, and put an `x` in all the boxes that
apply.  If you're unsure about any of these, don't hesitate to ask. We're here
to help! -->

-   [ ] I have run `python util/check_style.py --apply` to apply Open3D **code style**
    to my code.
-   [ ] This PR changes Open3D behavior or adds new functionality.
    -   [ ] Both C++ (Doxygen) and Python (Sphinx / Google style) **documentation** is
        updated accordingly.
    -   [ ] I have added or updated C++ and / or Python **unit tests** OR included **test
        results** (e.g. screenshots or numbers) here.
-   [ ] I will follow up and update the code if C<PERSON> fails.
    <!-- In case I am unavailable later -->
-   [ ] For fork PRs, I have selected **Allow edits from maintainers**.

## Description

<!--- Describe your changes, with test results and screenshots as appropriate. Move unrelated changes, if any, to a separate PR. -->
