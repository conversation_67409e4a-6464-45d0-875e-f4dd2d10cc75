<?xml version="1.0"?>
<package>
  <name>surfel_fusion</name>
  <version>0.0.0</version>
  <description>The surfel_fusion package</description>

  <maintainer email="<EMAIL>">WANG Kaixuan</maintainer>


  <license>TODO</license>

  <buildtool_depend>catkin</buildtool_depend>
  <build_depend>cmake_modules</build_depend> <!-- for FindEigen.cmake -->
  <build_depend>roscpp</build_depend>
  <build_depend>roslib</build_depend>
  <build_depend>svo_msgs</build_depend>
  <build_depend>cv_bridge</build_depend>
  <build_depend>image_transport</build_depend>
  <build_depend>vikit_ros</build_depend>
  <build_depend>pcl_ros</build_depend>

  <!-- Dependencies needed after this package is compiled. -->
  <run_depend>roscpp</run_depend>
  <run_depend>roslib</run_depend>
  <run_depend>svo_msgs</run_depend>
  <run_depend>cv_bridge</run_depend>
  <run_depend>image_transport</run_depend>
  <run_depend>vikit_ros</run_depend>
  <run_depend>pcl_ros</run_depend>
  <export>

  </export>
</package>
