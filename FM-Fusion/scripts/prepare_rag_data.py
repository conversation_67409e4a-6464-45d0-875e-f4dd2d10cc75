import os
import torch
from utils import read_data_association, read_ram_tags, read_scan_pairs

def save_tags(tag:list, dir:str, delimiter:str=','):
    with open(dir, 'w') as f:
        for t in tag:
            f.write(t + '{} '.format(delimiter))
        f.write('\n')
        f.close()

def process_one_scene(scan_folder:str,
                      da_file:str,
                      scene_rag_folder:str,
                      frame_gap:int=50,
                      pred_name:str='prediction_no_augment'):
    assert os.path.exists(scan_folder), 'Folder not found: {}'.format(scan_folder)
    assert os.path.exists(scene_rag_folder), 'Folder not found: {}'.format(scene_rag_folder)
    
    scene_name = os.path.basename(scene_rag_folder)
    print('------------ Processing {} ------------'.format(scene_name))
    
    _, rgb_frames = read_data_association(os.path.join(scan_folder, da_file))
    frames = [os.path.basename(frame)[:-4] for frame in rgb_frames]
    print('Loaded {} frames of tags'.format(len(rgb_frames)))

    scene_tags = []
    latest_id = -100
    
    for frame in frames:
        frame_id = int(frame[-6:])
        if frame_id - latest_id < frame_gap:
            continue
        pred_file = os.path.join(scan_folder, pred_name, '{}_label.json'.format(frame))
        if os.path.exists(pred_file) == False: 
            print('Skip File not found: {}'.format(pred_file))
            continue
        frame_ram_tags = read_ram_tags(pred_file)
        # print(frame_ram_tags)
    
        for tag in frame_ram_tags:
            if tag not in scene_tags:
                scene_tags.append(tag)
    
        latest_id = frame_id
        
    save_tags(scene_tags, os.path.join(scene_rag_folder, 'tags.txt'))
    print('Save {} tags to {}'.format(len(scene_tags), 
                                      os.path.join(scene_rag_folder, 'tags.txt')))
    # print('Scene tags: {}'.format(scene_tags))
    
        

if __name__=='__main__':
    print('##############################################')
    print('Read the tags of frames in a scene, where the tags are generated by RAM')
    print('Create a vector of tags for the scene. ', 'Save the scene tags to a file')
    print('##############################################')
    
    #############   SET CONFIG   #############
    SCAN_ROOT = '/data2/ScanNet'
    SPLIT = 'val'
    RAG_DATAROOT = '/data2/ScanNetRag'
    SPLIT_FILE = '/data2/ScanNetGraph/splits/{}.txt'.format(SPLIT)
    ##########################################
    
    scan_pairs = read_scan_pairs(SPLIT_FILE)
    print('Loaded {} scan pairs'.format(len(scan_pairs)))
    
    for scan_pair in scan_pairs:    
        # scene = 'scene0064_00'
        # posfix = 'c'
        src_scene, ref_scene = scan_pair
        scene_name = src_scene[:-1]
        posfix_src = src_scene[-1]
        posfix_ref = ref_scene[-1]
    
        da_file_src = 'data_association_{}.txt'.format(posfix_src)
        da_file_ref = 'data_association_{}.txt'.format(posfix_ref)
        
        process_one_scene(os.path.join(SCAN_ROOT, SPLIT, scene_name),
                        da_file_src,
                        os.path.join(RAG_DATAROOT, SPLIT, '{}{}'.format(scene_name, posfix_src)))
    
        process_one_scene(os.path.join(SCAN_ROOT, SPLIT, scene_name),
                        da_file_ref,
                        os.path.join(RAG_DATAROOT, SPLIT, '{}{}'.format(scene_name, posfix_ref)))
    
        