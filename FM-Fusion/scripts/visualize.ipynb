{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Load geometric reconstruction"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os, sys\n", "import open3d as o3d \n", "import numpy as np\n", "import rerun \n", "\n", "scene_folder = '/data2/matterport3d/output/debug'\n", "map_filename = 'floor1_o3d.ply'\n", "\n", "pcd = o3d.io.read_point_cloud(os.path.join(scene_folder, map_filename))\n", "print('Load {} points from {}'.format(len(pcd.points), map_filename))\n", "\n", "rerun.init(\"rerun_visualization\")\n", "rerun.log(\"global_pcd\", rerun.Points3D(np.asarray(pcd.points), \n", "                                       colors=np.asarray(pcd.colors)))\n", "# rerun.notebook_show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load semantic map"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["instance_map_filename = 'instance_map.ply' # 'pointcloud_o3d.ply'\n", "\n", "inst_pcd = o3d.io.read_point_cloud(os.path.join(scene_folder,\n", "                                                '2azQ1b91cZZ', \n", "                                                instance_map_filename))\n", "\n", "rerun.log(\"instance_pcd\", rerun.Points3D(np.asarray(inst_pcd.points),\n", "                                       colors=np.asarray(inst_pcd.colors)))\n", "rerun.notebook_show(width=900, height=600)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Visualize"]}], "metadata": {"kernelspec": {"display_name": "sgnet", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}