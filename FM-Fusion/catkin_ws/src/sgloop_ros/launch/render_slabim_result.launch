<launch>
  <arg name="cfg_file" value="$(find sgloop_ros)/../../../config/slabim.yaml"/>
  <arg name="result_folder" value="/data2/slabim/output/online_mapping"/>
  <arg name="agentA_scene" value="xb0101_02"/>

  <group ns="viz">
    <param name="edge_width" value="0.03"/>
    <param name="edge_color/r" value="0.0"/>
    <param name="edge_color/g" value="1.0"/>
    <param name="edge_color/b" value="1.0"/>
    <param name="centroid_size" value="0.3"/>
    <param name="centroid_v_offset" value="3.0"/>
    <param name="annotation_size" value="0.7"/>
    <param name="annotation_v_offset" value="0.3"/>
  </group>

  <node pkg="sgloop_ros" name="agentA" type="RenderNode" output="screen" clear_params="true">
      <param name="agent_name" value="agentA"/>
      <param name="cfg_file" value="$(arg cfg_file)"/>
      <param name="sequence_result_folder" value="$(arg result_folder)/$(arg agentA_scene)"/>
      <param name="map_name" value=""/>
      <!-- <param name="map_name" value="clean_colorized.ply"/> -->
  </node>

  <node pkg="tf" type="static_transform_publisher" name="link_1st_agent" args="0 0 0 0 0 0 1 world agentA 100" />

</launch>
