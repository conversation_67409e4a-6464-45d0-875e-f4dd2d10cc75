<launch>
  <!-- Debug launch file for online mapping -->
  
  <node pkg="sgloop_ros" name="online_mapping_debug" type="OnlineMappingNode" 
        output="screen" launch-prefix="gdb -ex run --args">
    <param name="cfg_file" value="$(find sgloop_ros)/../../../config/online.yaml"/>
    <param name="local_agent" value="agent0"/>
    <param name="output_folder" value="$(find sgloop_ros)/../../../output/online_test"/>
    <param name="max_frames" value="10"/>
  </node>
  
</launch>
