<launch>
  <!-- Online Semantic Mapping V2 Launch File -->
  <!-- This version uses synchronized ROS topics as input -->
  
  <!-- Configuration -->
  <arg name="cfg_file" default="$(find sgloop_ros)/../../../config/online.yaml"/>
  <arg name="output_folder" default="$(find sgloop_ros)/../../../output/online_mapping_v2"/>
  <arg name="local_agent" default="agent0"/>
  <arg name="max_frames" default="1000"/>
  <arg name="frame_gap" default="1"/>
  <arg name="camera_frame" default="camera_color_optical_frame"/>
  <arg name="world_frame" default="map"/>
  
  <!-- Visualization settings -->
  <group ns="viz"> 
      <param name="edge_width" value="0.03"/>
      <param name="edge_color/r" value="1.0"/>
      <param name="edge_color/g" value="0.5"/>
      <param name="edge_color/b" value="0.0"/>
      <param name="centroid_size" value="0.2"/>
      <param name="centroid_color/r" value="1.0"/>
      <param name="centroid_color/g" value="0.0"/>
      <param name="centroid_color/b" value="0.0"/>
      <param name="annotation_size" value="0.3"/>
      <param name="centroid_v_offset" value="0.0"/>
      <param name="annotation_v_offset" value="0.3"/>
  </group>

  <!-- Online Mapping Node V2 -->
  <node pkg="sgloop_ros" name="$(arg local_agent)" type="OnlineMappingNodeV2" output="screen" clear_params="true">
    <param name="cfg_file" value="$(arg cfg_file)"/>
    <param name="output_folder" value="$(arg output_folder)"/>
    <param name="local_agent" value="$(arg local_agent)"/>
    <param name="max_frames" value="$(arg max_frames)"/>
    <param name="frame_gap" value="$(arg frame_gap)"/>
    <param name="camera_frame" value="$(arg camera_frame)"/>
    <param name="world_frame" value="$(arg world_frame)"/>
    <param name="visualization" value="1"/>
    <param name="debug" value="false"/>
  </node>

  <!-- Transform publisher for the agent -->
  <node pkg="tf" type="static_transform_publisher" name="link_online_agent" 
        args="0 0 0 0 0 0 1 world $(arg local_agent) 100" />

</launch>
