<launch>
  <!-- SET CONFIGURATION HERE -->
  <arg name="dataroot" value="/data2/matterport3d"/>
  <arg name="sequence_name" default="2azQ1b91cZZ"/>

  <!-- global settings  -->
  <arg name="cfg_file" value="$(find sgloop_ros)/../../../config/matterport3d.yaml"/>
  <arg name="verbose_level" value="0"/>
  <arg name="output_folder" value="$(arg dataroot)/output/floor1"/>

  <group ns="viz"> 
      <param name="edge_width" value="0.03"/>
      <param name="edge_color/r" value="0.0"/>
      <param name="edge_color/g" value="1.0"/>
      <param name="edge_color/b" value="1.0"/>
      <param name="centroid_size" value="0.2"/>
      <param name="centroid_color/r" value="0.0"/>
      <param name="centroid_color/g" value="0.0"/>
      <param name="centroid_color/b" value="0.0"/>
      <param name="annotation_size" value="0.3"/>
      <param name="centroid_v_offset" value="0.0"/>
      <param name="annotation_v_offset" value="0.3"/>
  </group>

  <node pkg="sgloop_ros" name="agentA" type="MappingNode" output="screen" clear_params="true">
    <param name="cfg_file" value="$(arg cfg_file)"/>
    <param name="output_folder" value="$(arg output_folder)"/>
    <param name="active_sequence_dir" value="$(arg dataroot)/v1/scans/$(arg sequence_name)"/>
    <param name="local_agent" value="agentA"/>
    <param name="association_name" value="data_association_floor1.txt"/>
    <param name="trajectory_name" value="trajectory_floor1.log"/>
    <param name="max_frames" value="500"/>
    <param name="frame_gap" value="1"/>
    <param name="visualization" value="1"/>
    <param name="o3d_verbose_level" value="$(arg verbose_level)"/>
  </node>

  <node pkg="tf" type="static_transform_publisher" name="link_1st_agent" args="0 0 0 0 0 0 1 world agentA 100" />

</launch>
