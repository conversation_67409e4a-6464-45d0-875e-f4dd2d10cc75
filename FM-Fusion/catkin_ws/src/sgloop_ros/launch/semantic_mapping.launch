<launch>
  <!-- SET CONFIGURATION HERE -->
  <arg name="dataroot" default="$(find sgloop_ros)/../../../data/ScanNet"/>
  <arg name="sequence_name" default="scene0025_00"/>
  <!-- <arg name="sequence_name" default="ab0201_03a"/> -->

  <!-- global settings  -->
  <arg name="cfg_file" default="$(find sgloop_ros)/../../../config/scannet_offline.yaml"/>
  <arg name="verbose_level" value="0"/>
  <arg name="output_folder" value="$(arg dataroot)/output/online_mapping"/>

  <group ns="viz"> 
      <param name="edge_width" value="0.03"/>
      <param name="edge_color/r" value="0.0"/>
      <param name="edge_color/g" value="1.0"/>
      <param name="edge_color/b" value="1.0"/>
      <param name="centroid_size" value="0.2"/>
      <param name="centroid_color/r" value="0.0"/>
      <param name="centroid_color/g" value="0.0"/>
      <param name="centroid_color/b" value="0.0"/>
      <param name="annotation_size" value="0.3"/>
      <param name="centroid_v_offset" value="0.0"/>
      <param name="annotation_v_offset" value="0.3"/>
  </group>

  <!-- Online Mapping Node V2 for synchronized topics -->
  <node pkg="sgloop_ros" name="agentA" type="OnlineMappingNodeV2" output="screen" clear_params="true">
    <param name="cfg_file" value="$(find sgloop_ros)/../../../config/online.yaml"/>
    <param name="output_folder" value="$(arg output_folder)"/>
    <param name="local_agent" value="agentA"/>
    <param name="max_frames" value="200"/>        <!-- 从500减少到200 -->
    <param name="frame_gap" value="5"/>           <!-- 从1增加到5 -->
    <param name="camera_frame" value="camera_color_optical_frame"/>
    <param name="world_frame" value="map"/>
    <param name="visualization" value="1"/>
    <param name="debug" value="false"/>
  </node>

  <node pkg="tf" type="static_transform_publisher" name="link_1st_agent" args="0 0 0 0 0 0 1 world agentA 100" />

</launch>
