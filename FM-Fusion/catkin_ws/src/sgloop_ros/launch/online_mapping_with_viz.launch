<launch>
  <!-- Complete Online Semantic Mapping with Visualization -->
  <!-- This launch file starts both the online mapping node and RViz visualization -->
  
  <!-- Configuration -->
  <arg name="cfg_file" default="$(find sgloop_ros)/../../../config/online.yaml"/>
  <arg name="output_folder" default="$(find sgloop_ros)/../../../output/online_mapping"/>
  <arg name="local_agent" default="agentA"/>
  <arg name="max_frames" default="1000"/>
  <arg name="verbose_level" default="0"/>
  <arg name="enable_rviz" default="true"/>
  
  <!-- Visualization settings -->
  <group ns="viz"> 
      <param name="edge_width" value="0.03"/>
      <param name="edge_color/r" value="1.0"/>
      <param name="edge_color/g" value="0.5"/>
      <param name="edge_color/b" value="0.0"/>
      <param name="centroid_size" value="0.2"/>
      <param name="centroid_color/r" value="1.0"/>
      <param name="centroid_color/g" value="0.0"/>
      <param name="centroid_color/b" value="0.0"/>
      <param name="annotation_size" value="0.3"/>
      <param name="centroid_v_offset" value="0.0"/>
      <param name="annotation_v_offset" value="0.3"/>
  </group>

  <!-- Online Mapping Node -->
  <node pkg="sgloop_ros" name="$(arg local_agent)" type="OnlineMappingNode" output="screen" clear_params="true">
    <param name="cfg_file" value="$(arg cfg_file)"/>
    <param name="output_folder" value="$(arg output_folder)"/>
    <param name="local_agent" value="$(arg local_agent)"/>
    <param name="max_frames" value="$(arg max_frames)"/>
    <param name="visualization" value="1"/>
    <param name="o3d_verbose_level" value="$(arg verbose_level)"/>
  </node>

  <!-- Transform publisher for the agent -->
  <node pkg="tf" type="static_transform_publisher" name="link_online_agent"
        args="0 0 0 0 0 0 1 world $(arg local_agent) 100" />

  <!-- Additional transform for base_link (needed for RViz) -->
  <node pkg="tf" type="static_transform_publisher" name="link_base_link"
        args="0 0 0 0 0 0 1 $(arg local_agent) base_link 100" />

  <!-- RViz Visualization (optional) -->
  <node name="rviz" pkg="rviz" type="rviz" args="-d $(find sgloop_ros)/launch/mapping.rviz"
        if="$(arg enable_rviz)" />

</launch>
