<launch>

  <arg name="dataroot" value="/data2/sgslam"/>
  <!-- <arg name="agentA_scene" value="uc0204_00a"/> -->
  <!-- <arg name="agentB_scene" value="uc0204_00b"/> -->
  <arg name="agentA_scene" value="ab0201_03a"/>
  <arg name="agentB_scene" value="ab0201_03a"/>

  <arg name="agentA_agnetB_x" value="0"/>
  <arg name="agentA_agentB_y" value="0"/>
  <arg name="agentA_agentB_z" value="-4.0"/>
  <arg name="agentA_agentB_yaw" value="0"/>

  <group ns="viz">
    <param name="edge_width" value="0.03"/>
    <param name="edge_color/r" value="0.0"/>
    <param name="edge_color/g" value="1.0"/>
    <param name="edge_color/b" value="1.0"/>
    <param name="centroid_size" value="0.3"/>
  </group>

  <!-- inital tf to enable visulization -->
  <group ns="br">
    <param name="agentA/x" value="0.0"/>
    <param name="agentA/y" value="0.0"/>
    <param name="agentA/z" value="0.0"/>

    <param name="agentB/x" value="$(arg agentA_agnetB_x)"/>
    <param name="agentB/y" value="$(arg agentA_agentB_y)"/>
    <param name="agentB/z" value="$(arg agentA_agentB_z)"/>
    <param name="agentB/yaw" value="$(arg agentA_agentB_yaw)"/>
  </group>

  <node pkg="sgloop_ros" name="agentA" type="PoseGraphNode" output="screen" clear_params="true">
      <param name="cfg_file" value="/home/<USER>/code_ws/OpensetFusion/config/realsense.yaml"/>
      <param name="src_name" value="agentA"/>
      <param name="ref_name" value="agentB"/>
      <param name="camera_marker/r" value="0.8"/>
      <param name="camera_marker/g" value="0.0"/>
      <param name="camera_marker/b" value="0.8"/>
      <param name="camera_marker/scale" value="0.3"/>
      <param name="camera_marker/line_width" value="0.05"/>
      <param name="map_name" value="mesh_o3d.ply"/>
      <!-- <param name="map_name" value="frame-000671_src.ply"/> -->
      <!-- <param name="select_frame" value="frame-000671"/> -->
      <param name="mode" value="15"/>
      <param name="loop_filename" value="loops_tp_raw.txt"/>
      <!-- <param name="pose_graph_folder" value="/data2/sgslam/output/v9/$(arg agentA_scene)/$(arg agentB_scene)"/> -->
      <param name="pose_graph_folder" value="/data2/sgslam/output/v9/$(arg agentA_scene)"/>
      <param name="src_scene_dir" value="$(arg dataroot)/scans/$(arg agentA_scene)"/>
      <param name="ref_scene_dir" value="$(arg dataroot)/scans/$(arg agentB_scene)"/>
      <param name="gt_file" value="$(arg dataroot)/gt/$(arg agentA_scene)-$(arg agentB_scene).txt"/>
      <param name="ate_threshold" value="0.2"/>
      <param name="render_loops" value="true"/>
      <param name="render_trajectory" value="true"/>
  </node>

  <!-- <node pkg="sgloop_ros" name="agentB" type="PoseGraphNode" output="screen" clear_params="true">
      <param name="cfg_file" value="/home/<USER>/code_ws/OpensetFusion/config/realsense.yaml"/>
      <param name="src_name" value="agentB"/>
      <param name="ref_name" value="agentA"/>
      <param name="camera_marker/r" value="0.0"/>
      <param name="camera_marker/g" value="0.0"/>
      <param name="camera_marker/b" value="1.0"/>
      <param name="camera_marker/scale" value="0.3"/>
      <param name="camera_marker/line_width" value="0.05"/>
      <param name="map_name" value="mesh_o3d.ply"/>
      <param name="mode" value="99"/>
      <param name="pose_graph_folder" value="/data2/sgslam/output/v9/$(arg agentB_scene)/fakeScene"/>
      <param name="src_scene_dir" value="$(arg dataroot)/scans/$(arg agentB_scene)"/>
      <param name="render_loops" value="false"/>
      <param name="render_trajectory" value="true"/>
  </node> -->

  <node pkg="tf" type="static_transform_publisher" name="link_1st_agent" args="0 0 0 0 0 0 1 world agentA 100" />
  <node pkg="tf" type="static_transform_publisher" name="link_2nd_agent" args="$(arg agentA_agnetB_x) $(arg agentA_agentB_y) $(arg agentA_agentB_z) 0 0 0 1 world agentB 100" />

</launch>
