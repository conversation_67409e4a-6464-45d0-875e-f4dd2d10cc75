<!-- 创建新文件 catkin_ws/src/sgloop_ros/launch/custom_render_semantic_map.launch -->
<launch>
  <arg name="result_folder" default="$(find sgloop_ros)/../../../data/ScanNet/output/online_mapping"/>
  <arg name="cfg_file" default="$(find sgloop_ros)/../../../config/scannet.yaml"/>
  <arg name="agentA_scene" default="scene0025_00"/>

  <group ns="viz">
    <param name="edge_width" value="0.03"/>
    <param name="edge_color/r" value="0.0"/>
    <param name="edge_color/g" value="1.0"/>
    <param name="edge_color/b" value="1.0"/>
    <param name="centroid_size" value="0.2"/>
    <param name="centroid_v_offset" value="3.0"/>
    <param name="annotation_size" value="0.3"/>
    <param name="annotation_v_offset" value="0.3"/>
  </group>

  <node pkg="sgloop_ros" name="agentA" type="RenderNode" output="screen" clear_params="true">
      <param name="agent_name" value="agentA"/>
      <param name="cfg_file" value="$(arg cfg_file)"/>
      <param name="sequence_result_folder" value="$(arg result_folder)/$(arg agentA_scene)"/>
      <param name="map_name" value=""/>
      <!-- <param name="map_name" value="mesh_o3d.ply"/> -->
  </node>

  <node pkg="tf" type="static_transform_publisher" name="link_1st_agent" args="0 0 0 0 0 0 1 world agentA 100" />

</launch>