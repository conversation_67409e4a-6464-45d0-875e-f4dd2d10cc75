<launch>
  <!-- 机器狗键盘控制启动文件 -->
  
  <!-- 参数配置 -->
  <arg name="linear_speed" default="0.5"/>     <!-- 默认线速度 m/s -->
  <arg name="angular_speed" default="1.0"/>    <!-- 默认角速度 rad/s -->
  <arg name="speed_step" default="0.1"/>       <!-- 速度调节步长 -->
  <arg name="cmd_topic" default="/cmd_vel"/>   <!-- 控制话题名称 -->
  
  <!-- 键盘控制节点 -->
  <node pkg="sgloop_ros" name="robot_keyboard_controller" type="robot_keyboard_control.py" 
        output="screen" launch-prefix="xterm -e">
    <param name="linear_speed" value="$(arg linear_speed)"/>
    <param name="angular_speed" value="$(arg angular_speed)"/>
    <param name="speed_step" value="$(arg speed_step)"/>
    <remap from="/cmd_vel" to="$(arg cmd_topic)"/>
  </node>
  
  <!-- 可选：启动rviz可视化 -->
  <arg name="enable_rviz" default="false"/>
  <node if="$(arg enable_rviz)" pkg="rviz" name="rviz" type="rviz" 
        args="-d $(find sgloop_ros)/rviz/robot_control.rviz"/>
        
</launch>
