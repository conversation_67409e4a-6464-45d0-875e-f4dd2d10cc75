<launch>
  <!-- Python Online Semantic Mapping Launch File -->
  
  <!-- Configuration -->
  <arg name="config_file" default="$(find sgloop_ros)/../../../config/online.yaml"/>
  <arg name="output_folder" default="$(find sgloop_ros)/../../../output/python_online_mapping"/>
  <arg name="max_frames" default="1000"/>
  <arg name="frame_gap" default="1"/>
  
  <!-- Camera and TF settings -->
  <arg name="camera_frame" default="camera_color_optical_frame"/>
  <arg name="world_frame" default="map"/>
  
  <!-- Python Online Mapping Node -->
  <node pkg="sgloop_ros" name="python_online_mapping" type="online_mapping_node.py" output="screen">
    <param name="config_file" value="$(arg config_file)"/>
    <param name="output_folder" value="$(arg output_folder)"/>
    <param name="max_frames" value="$(arg max_frames)"/>
    <param name="frame_gap" value="$(arg frame_gap)"/>
    <param name="camera_frame" value="$(arg camera_frame)"/>
    <param name="world_frame" value="$(arg world_frame)"/>
  </node>

  <!-- RViz for visualization -->
  <node pkg="rviz" type="rviz" name="rviz" 
        args="-d $(find sgloop_ros)/rviz/online_mapping.rviz" 
        if="$(arg launch_rviz)" />
  
  <arg name="launch_rviz" default="true"/>

</launch>
