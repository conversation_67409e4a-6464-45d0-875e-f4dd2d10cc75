<launch>
  <!-- Complete test setup for Python online mapping -->
  
  <!-- Configuration -->
  <arg name="config_file" default="$(find sgloop_ros)/../../../config/online.yaml"/>
  <arg name="output_folder" default="$(find sgloop_ros)/../../../output/python_online_test"/>
  <arg name="data_folder" default="$(find sgloop_ros)/../../../data/ScanNet/scene0651_00"/>
  <arg name="max_frames" default="50"/>
  <arg name="frame_rate" default="2.0"/>
  <arg name="start_frame" default="0"/>
  
  <!-- Test Data Publisher -->
  <node pkg="sgloop_ros" name="test_data_publisher" type="test_data_publisher.py" output="screen">
    <param name="data_folder" value="$(arg data_folder)"/>
    <param name="frame_rate" value="$(arg frame_rate)"/>
    <param name="start_frame" value="$(arg start_frame)"/>
    <param name="max_frames" value="$(arg max_frames)"/>
  </node>

  <!-- Python Online Mapping Node -->
  <node pkg="sgloop_ros" name="python_online_mapping" type="online_mapping_node.py" output="screen">
    <param name="config_file" value="$(arg config_file)"/>
    <param name="output_folder" value="$(arg output_folder)"/>
    <param name="max_frames" value="$(arg max_frames)"/>
    <param name="frame_gap" value="1"/>
    <param name="camera_frame" value="camera_color_optical_frame"/>
    <param name="world_frame" value="map"/>
  </node>

  <!-- RViz for visualization -->
  <node pkg="rviz" type="rviz" name="rviz" output="screen"/>

</launch>
