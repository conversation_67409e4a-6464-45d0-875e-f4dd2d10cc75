<launch>
  <!-- Test setup for Online Mapping V2 with your data format -->
  
  <!-- Configuration -->
  <arg name="cfg_file" default="$(find sgloop_ros)/../../../config/online.yaml"/>
  <arg name="output_folder" default="$(find sgloop_ros)/../../../output/my_data_online_mapping"/>
  <arg name="data_folder" default="$(find sgloop_ros)/../../../data/ScanNet/my_Data"/>
  <arg name="max_frames" default="10"/>
  <arg name="frame_rate" default="0.5"/>
  <arg name="start_frame" default="0"/>
  
  <!-- My Data Publisher (Python) -->
  <node pkg="sgloop_ros" name="my_data_publisher" type="my_data_publisher.py" output="screen">
    <param name="data_folder" value="$(arg data_folder)"/>
    <param name="frame_rate" value="$(arg frame_rate)"/>
    <param name="start_frame" value="$(arg start_frame)"/>
    <param name="max_frames" value="$(arg max_frames)"/>
  </node>

  <!-- Online Mapping Node V2 (C++) -->
  <node pkg="sgloop_ros" name="online_mapping_v2" type="OnlineMappingNodeV2" output="screen">
    <param name="cfg_file" value="$(arg cfg_file)"/>
    <param name="output_folder" value="$(arg output_folder)"/>
    <param name="local_agent" value="agent0"/>
    <param name="max_frames" value="$(arg max_frames)"/>
    <param name="frame_gap" value="1"/>
    <param name="camera_frame" value="camera_color_optical_frame"/>
    <param name="world_frame" value="map"/>
    <param name="visualization" value="1"/>
    <param name="debug" value="false"/>
  </node>

  <!-- RViz for visualization -->
  <node pkg="rviz" type="rviz" name="rviz" output="screen"/>

</launch>
