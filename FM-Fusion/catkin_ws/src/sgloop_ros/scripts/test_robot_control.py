#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
机器狗控制测试脚本
用于测试cmd_vel话题是否正常工作
"""

import rospy
from geometry_msgs.msg import Twist
from std_msgs.msg import String

def cmd_vel_callback(msg):
    """cmd_vel话题回调函数"""
    print(f"收到控制命令:")
    print(f"  线速度: x={msg.linear.x:.2f}, y={msg.linear.y:.2f}, z={msg.linear.z:.2f}")
    print(f"  角速度: x={msg.angular.x:.2f}, y={msg.angular.y:.2f}, z={msg.angular.z:.2f}")
    print("-" * 50)

def status_callback(msg):
    """状态话题回调函数"""
    print(f"控制状态: {msg.data}")

def main():
    # 初始化ROS节点
    rospy.init_node('robot_control_tester', anonymous=True)
    
    print("=== 机器狗控制测试器 ===")
    print("监听 /cmd_vel 话题...")
    print("启动键盘控制器后，这里会显示收到的控制命令")
    print("按 Ctrl+C 退出")
    print("=" * 50)
    
    # 订阅控制话题
    rospy.Subscriber('/cmd_vel', Twist, cmd_vel_callback)
    rospy.Subscriber('/robot_status', String, status_callback)
    rospy.Subscriber('/robot_control_status', String, status_callback)
    
    try:
        rospy.spin()
    except KeyboardInterrupt:
        print("\n测试器已退出")

if __name__ == '__main__':
    main()
