# 机器狗键盘控制系统

这个包提供了多种方式来通过键盘控制机器狗的移动。

## 文件说明

### 1. `robot_keyboard_control.py` - 基础键盘控制
简单的键盘控制脚本，支持基本的移动控制。

**特点:**
- 简单易用的按键控制
- 实时速度调节
- 紧急停止功能

**控制按键:**
- `W/S`: 前进/后退
- `A/D`: 左转/右转  
- `Q/E`: 左平移/右平移
- `R/F`: 增加/减少线速度
- `T/G`: 增加/减少角速度
- `空格`: 紧急停止
- `H`: 显示帮助
- `X`: 退出

### 2. `advanced_robot_control.py` - 高级控制
功能更丰富的控制脚本，支持连续移动和实时状态显示。

**特点:**
- 支持按键组合（如同时前进和转弯）
- 平滑加速/减速
- 实时状态显示界面
- 机器人位置信息显示
- 更精细的控制

**额外控制按键:**
- `Z/C`: 原地左转/右转
- `P`: 暂停/恢复
- `I`: 显示机器人状态

### 3. `test_robot_control.py` - 测试工具
用于测试控制命令是否正常发送的监听工具。

## 使用方法

### 方法1: 直接运行Python脚本

```bash
# 基础控制
cd /home/<USER>/ros_ws/src/FM-Fusion/catkin_ws
source devel/setup.bash
rosrun sgloop_ros robot_keyboard_control.py

# 高级控制
rosrun sgloop_ros advanced_robot_control.py

# 测试工具
rosrun sgloop_ros test_robot_control.py
```

### 方法2: 使用Launch文件

```bash
# 使用launch文件启动基础控制
roslaunch sgloop_ros robot_keyboard_control.launch

# 自定义参数启动
roslaunch sgloop_ros robot_keyboard_control.launch linear_speed:=0.8 angular_speed:=1.5
```

### 方法3: 测试控制系统

1. 在一个终端启动测试监听器:
```bash
rosrun sgloop_ros test_robot_control.py
```

2. 在另一个终端启动控制器:
```bash
rosrun sgloop_ros robot_keyboard_control.py
```

3. 在控制器中按键，观察测试器是否收到命令

## 参数配置

可以通过ROS参数调整控制器行为:

```bash
# 设置最大线速度为0.8 m/s
rosparam set /robot_keyboard_controller/linear_speed 0.8

# 设置最大角速度为1.5 rad/s  
rosparam set /robot_keyboard_controller/angular_speed 1.5

# 设置速度调节步长
rosparam set /robot_keyboard_controller/speed_step 0.05
```

## 话题说明

### 发布的话题:
- `/cmd_vel` (geometry_msgs/Twist): 机器人控制命令
- `/robot_status` (std_msgs/String): 基础控制器状态
- `/robot_control_status` (std_msgs/String): 高级控制器状态

### 订阅的话题 (高级控制器):
- `/odom` (nav_msgs/Odometry): 机器人里程计信息

## 安全注意事项

1. **紧急停止**: 所有控制器都支持紧急停止功能（空格键）
2. **速度限制**: 内置最大速度限制，防止过快移动
3. **平滑控制**: 高级控制器提供平滑加速，避免突然启停
4. **状态监控**: 实时显示当前控制状态

## 故障排除

### 问题1: 控制器启动失败
```bash
# 检查ROS环境
echo $ROS_MASTER_URI
roscore  # 确保roscore在运行

# 检查Python权限
ls -la /home/<USER>/ros_ws/src/FM-Fusion/catkin_ws/src/sgloop_ros/scripts/
```

### 问题2: 机器人不响应控制命令
```bash
# 检查话题是否发布
rostopic list | grep cmd_vel
rostopic echo /cmd_vel

# 检查机器人是否订阅了cmd_vel话题
rostopic info /cmd_vel
```

### 问题3: 键盘输入无响应
- 确保终端窗口处于活动状态
- 检查是否有其他程序占用键盘输入
- 尝试重启控制器

## 扩展开发

如需自定义控制逻辑，可以参考现有脚本的结构:

1. 继承基础控制器类
2. 重写按键处理函数
3. 添加自定义控制逻辑
4. 实现特定的安全检查

## 兼容性

- ROS Noetic
- Python 3.x
- 支持标准的geometry_msgs/Twist消息格式
- 兼容大多数移动机器人平台
