#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
机器狗键盘控制节点
使用键盘控制机器狗的移动，发布cmd_vel话题
"""

import rospy
import sys
import select
import termios
import tty
from geometry_msgs.msg import Twist
from std_msgs.msg import String

class RobotKeyboardController:
    def __init__(self):
        # 初始化ROS节点
        rospy.init_node('robot_keyboard_controller', anonymous=True)
        
        # 创建发布者
        self.cmd_vel_pub = rospy.Publisher('/cmd_vel', Twist, queue_size=1)
        self.status_pub = rospy.Publisher('/robot_status', String, queue_size=1)
        
        # 运动参数
        self.linear_speed = rospy.get_param('~linear_speed', 0.5)  # 线速度 m/s
        self.angular_speed = rospy.get_param('~angular_speed', 1.0)  # 角速度 rad/s
        self.speed_step = rospy.get_param('~speed_step', 0.1)  # 速度调节步长
        
        # 当前速度
        self.current_linear = 0.0
        self.current_angular = 0.0
        
        # 保存终端设置
        self.settings = termios.tcgetattr(sys.stdin)
        
        # 控制说明
        self.control_info = """
=== 机器狗键盘控制 ===

移动控制:
    w/s : 前进/后退
    a/d : 左转/右转
    q/e : 左平移/右平移
    
速度控制:
    r/f : 增加/减少线速度
    t/g : 增加/减少角速度
    
其他:
    空格 : 紧急停止
    h    : 显示帮助
    x    : 退出程序

当前设置:
    线速度: {:.2f} m/s
    角速度: {:.2f} rad/s
    
按任意键开始控制...
        """.format(self.linear_speed, self.angular_speed)
        
        print(self.control_info)
        
    def get_key(self):
        """获取键盘输入"""
        tty.setraw(sys.stdin.fileno())
        select.select([sys.stdin], [], [], 0)
        key = sys.stdin.read(1)
        termios.tcsetattr(sys.stdin, termios.TCSADRAIN, self.settings)
        return key
    
    def publish_twist(self, linear_x=0.0, linear_y=0.0, angular_z=0.0):
        """发布Twist消息"""
        twist = Twist()
        twist.linear.x = linear_x
        twist.linear.y = linear_y
        twist.linear.z = 0.0
        twist.angular.x = 0.0
        twist.angular.y = 0.0
        twist.angular.z = angular_z
        
        self.cmd_vel_pub.publish(twist)
        
        # 发布状态信息
        status_msg = "Linear: ({:.2f}, {:.2f}), Angular: {:.2f}".format(
            linear_x, linear_y, angular_z)
        self.status_pub.publish(String(data=status_msg))
    
    def emergency_stop(self):
        """紧急停止"""
        self.publish_twist(0.0, 0.0, 0.0)
        self.current_linear = 0.0
        self.current_angular = 0.0
        print("\n[紧急停止] 机器人已停止")
    
    def update_speed_display(self):
        """更新速度显示"""
        print("\r当前速度 - 线速度: {:.2f} m/s, 角速度: {:.2f} rad/s".format(
            self.linear_speed, self.angular_speed), end='', flush=True)
    
    def run(self):
        """主控制循环"""
        try:
            while not rospy.is_shutdown():
                key = self.get_key()
                
                # 移动控制
                if key == 'w':  # 前进
                    self.publish_twist(self.linear_speed, 0.0, 0.0)
                    print("\n[前进] 速度: {:.2f} m/s".format(self.linear_speed))
                    
                elif key == 's':  # 后退
                    self.publish_twist(-self.linear_speed, 0.0, 0.0)
                    print("\n[后退] 速度: {:.2f} m/s".format(self.linear_speed))
                    
                elif key == 'a':  # 左转
                    self.publish_twist(0.0, 0.0, self.angular_speed)
                    print("\n[左转] 角速度: {:.2f} rad/s".format(self.angular_speed))
                    
                elif key == 'd':  # 右转
                    self.publish_twist(0.0, 0.0, -self.angular_speed)
                    print("\n[右转] 角速度: {:.2f} rad/s".format(self.angular_speed))
                    
                elif key == 'q':  # 左平移
                    self.publish_twist(0.0, self.linear_speed, 0.0)
                    print("\n[左平移] 速度: {:.2f} m/s".format(self.linear_speed))
                    
                elif key == 'e':  # 右平移
                    self.publish_twist(0.0, -self.linear_speed, 0.0)
                    print("\n[右平移] 速度: {:.2f} m/s".format(self.linear_speed))
                
                # 速度调节
                elif key == 'r':  # 增加线速度
                    self.linear_speed += self.speed_step
                    self.linear_speed = min(self.linear_speed, 2.0)  # 最大限制
                    self.update_speed_display()
                    
                elif key == 'f':  # 减少线速度
                    self.linear_speed -= self.speed_step
                    self.linear_speed = max(self.linear_speed, 0.1)  # 最小限制
                    self.update_speed_display()
                    
                elif key == 't':  # 增加角速度
                    self.angular_speed += self.speed_step
                    self.angular_speed = min(self.angular_speed, 3.0)  # 最大限制
                    self.update_speed_display()
                    
                elif key == 'g':  # 减少角速度
                    self.angular_speed -= self.speed_step
                    self.angular_speed = max(self.angular_speed, 0.1)  # 最小限制
                    self.update_speed_display()
                
                # 其他控制
                elif key == ' ':  # 空格 - 紧急停止
                    self.emergency_stop()
                    
                elif key == 'h':  # 帮助
                    print(self.control_info)
                    
                elif key == 'x':  # 退出
                    print("\n[退出] 正在关闭控制器...")
                    self.emergency_stop()
                    break
                    
                else:
                    # 松开按键时停止
                    if key in ['w', 's', 'a', 'd', 'q', 'e']:
                        continue
                    else:
                        self.publish_twist(0.0, 0.0, 0.0)
                
                rospy.sleep(0.1)  # 控制频率
                
        except KeyboardInterrupt:
            print("\n[中断] 程序被用户中断")
            self.emergency_stop()
        except Exception as e:
            print(f"\n[错误] 发生异常: {e}")
            self.emergency_stop()
        finally:
            # 恢复终端设置
            termios.tcsetattr(sys.stdin, termios.TCSADRAIN, self.settings)
            print("\n程序已退出")

def main():
    try:
        controller = RobotKeyboardController()
        controller.run()
    except rospy.ROSInterruptException:
        print("ROS节点被中断")
    except Exception as e:
        print(f"启动失败: {e}")

if __name__ == '__main__':
    main()
