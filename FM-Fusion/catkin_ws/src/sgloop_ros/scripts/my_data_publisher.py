#!/usr/bin/env python3

import rospy
import cv2
import numpy as np
import json
import os
from pathlib import Path

# ROS messages
from sensor_msgs.msg import Image, CameraInfo
from std_msgs.msg import String
from cv_bridge import CvBridge
import tf2_ros
from geometry_msgs.msg import TransformStamped, PoseStamped
import tf.transformations as tf_trans

class MyDataPublisher:
    def __init__(self):
        rospy.init_node('my_data_publisher', anonymous=True)
        
        # Parameters
        self.data_folder = rospy.get_param('~data_folder', '/home/<USER>/ros_ws/src/FM-Fusion/data/ScanNet/my_Data')
        self.frame_rate = rospy.get_param('~frame_rate', 1.0)  # Hz
        self.start_frame = rospy.get_param('~start_frame', 0)
        self.max_frames = rospy.get_param('~max_frames', 100)
        
        # State
        self.current_frame = self.start_frame
        self.bridge = CvBridge()
        
        # Publishers
        self.rgb_pub = rospy.Publisher('/sync/rgb', Image, queue_size=1)
        self.depth_pub = rospy.Publisher('/sync/depth', Image, queue_size=1)
        self.camera_info_pub = rospy.Publisher('/camera/color/camera_info', CameraInfo, queue_size=1)
        self.detection_pub = rospy.Publisher('/sync/json', String, queue_size=1)
        self.detection_mask_pub = rospy.Publisher('/sync/mask', Image, queue_size=1)
        self.pose_pub = rospy.Publisher('/sync/pose', PoseStamped, queue_size=1)
        
        # TF broadcaster
        self.tf_broadcaster = tf2_ros.TransformBroadcaster()
        
        # Load camera intrinsics (default values for testing)
        self.camera_info = self.create_default_camera_info()
        
        # Timer for publishing
        self.timer = rospy.Timer(rospy.Duration(1.0 / self.frame_rate), self.publish_frame)
        
        rospy.loginfo(f"My data publisher initialized. Data folder: {self.data_folder}")
        rospy.loginfo(f"Publishing at {self.frame_rate} Hz, frames {self.start_frame} to {self.start_frame + self.max_frames}")
    
    def create_default_camera_info(self):
        """创建默认相机内参"""
        camera_info = CameraInfo()
        camera_info.header.frame_id = "camera_color_optical_frame"
        
        # Default camera parameters (adjust based on your camera)
        camera_info.width = 640
        camera_info.height = 480
        
        # Camera matrix K
        fx, fy, cx, cy = 525.0, 525.0, 320.0, 240.0
        camera_info.K = [fx, 0, cx, 0, fy, cy, 0, 0, 1]
        
        # Distortion parameters (assume no distortion)
        camera_info.D = [0, 0, 0, 0, 0]
        camera_info.distortion_model = "plumb_bob"
        
        # Projection matrix P
        camera_info.P = [fx, 0, cx, 0, 0, fy, cy, 0, 0, 0, 1, 0]
        
        return camera_info
    
    def load_pose(self, frame_id):
        """加载相机位姿"""
        pose_file = os.path.join(self.data_folder, f'frame-{frame_id:06d}.txt')

        if os.path.exists(pose_file):
            try:
                pose_matrix = np.loadtxt(pose_file)
                return pose_matrix
            except Exception as e:
                rospy.logwarn(f"Failed to load pose for frame {frame_id}: {e}")

        # Return identity matrix as default
        return np.eye(4)

    def matrix_to_pose_stamped(self, pose_matrix, timestamp, frame_id="map"):
        """将4x4变换矩阵转换为PoseStamped消息"""
        pose_msg = PoseStamped()
        pose_msg.header.stamp = timestamp
        pose_msg.header.frame_id = frame_id

        # Extract translation
        pose_msg.pose.position.x = pose_matrix[0, 3]
        pose_msg.pose.position.y = pose_matrix[1, 3]
        pose_msg.pose.position.z = pose_matrix[2, 3]

        # Extract rotation matrix and convert to quaternion
        rotation_matrix = pose_matrix[:3, :3]

        # Convert rotation matrix to quaternion using tf.transformations
        quaternion = tf_trans.quaternion_from_matrix(pose_matrix)

        pose_msg.pose.orientation.x = quaternion[0]
        pose_msg.pose.orientation.y = quaternion[1]
        pose_msg.pose.orientation.z = quaternion[2]
        pose_msg.pose.orientation.w = quaternion[3]

        return pose_msg
    
    def load_detection_data(self, frame_id):
        """加载检测数据"""
        # JSON file
        json_file = os.path.join(self.data_folder, f'frame-{frame_id:06d}_label.json')
        # Mask file  
        mask_file = os.path.join(self.data_folder, f'frame-{frame_id:06d}.png')
        
        detections_json = None
        mask_image = None
        
        # Load JSON
        if os.path.exists(json_file):
            try:
                with open(json_file, 'r') as f:
                    detections_json = json.load(f)
            except Exception as e:
                rospy.logwarn(f"Failed to load detection JSON for frame {frame_id}: {e}")
        
        # Load mask
        if os.path.exists(mask_file):
            try:
                mask_image = cv2.imread(mask_file, cv2.IMREAD_GRAYSCALE)  # Load as grayscale
            except Exception as e:
                rospy.logwarn(f"Failed to load detection mask for frame {frame_id}: {e}")
        
        return detections_json, mask_image
    
    def create_dummy_rgb_depth(self, frame_id):
        """创建虚拟的RGB和深度图像（用于测试）"""
        # Create a simple test RGB image
        rgb_image = np.zeros((480, 640, 3), dtype=np.uint8)
        rgb_image[:, :, 0] = 100  # Blue channel
        rgb_image[:, :, 1] = 150  # Green channel
        rgb_image[:, :, 2] = 200  # Red channel
        
        # Add some pattern
        cv2.putText(rgb_image, f'Frame {frame_id}', (50, 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        
        # Create a simple test depth image
        depth_image = np.full((480, 640), 1000, dtype=np.uint16)  # 1 meter depth in mm
        
        # Add some depth variation
        for i in range(480):
            for j in range(640):
                depth_image[i, j] = 800 + int(200 * np.sin(i * 0.01) * np.cos(j * 0.01))
        
        return rgb_image, depth_image
    
    def publish_frame(self, event):
        """发布一帧数据"""
        if self.current_frame >= self.start_frame + self.max_frames:
            rospy.loginfo("Reached max frames, stopping...")
            self.timer.shutdown()
            return
        
        frame_id = self.current_frame
        timestamp = rospy.Time.now()
        
        rospy.loginfo(f"Publishing frame {frame_id}")
        
        try:
            # Create dummy RGB and depth images (since we don't have real ones)
            rgb_image, depth_image = self.create_dummy_rgb_depth(frame_id)
            
            # Publish RGB image
            rgb_msg = self.bridge.cv2_to_imgmsg(rgb_image, "bgr8")
            rgb_msg.header.stamp = timestamp
            rgb_msg.header.frame_id = "camera_color_optical_frame"
            self.rgb_pub.publish(rgb_msg)
            
            # Publish depth image
            depth_msg = self.bridge.cv2_to_imgmsg(depth_image, "16UC1")
            depth_msg.header.stamp = timestamp
            depth_msg.header.frame_id = "camera_color_optical_frame"
            self.depth_pub.publish(depth_msg)
            
            # Publish camera info
            self.camera_info.header.stamp = timestamp
            self.camera_info_pub.publish(self.camera_info)
            
            # Load and publish detection data
            detections_json, mask_image = self.load_detection_data(frame_id)
            
            if detections_json is not None:
                detection_msg = String()
                detection_msg.data = json.dumps(detections_json)
                self.detection_pub.publish(detection_msg)
            
            if mask_image is not None:
                mask_msg = self.bridge.cv2_to_imgmsg(mask_image, "mono8")
                mask_msg.header.stamp = timestamp
                mask_msg.header.frame_id = "camera_color_optical_frame"
                self.detection_mask_pub.publish(mask_msg)
            
            # Load and publish pose
            pose_matrix = self.load_pose(frame_id)
            pose_msg = self.matrix_to_pose_stamped(pose_matrix, timestamp)
            self.pose_pub.publish(pose_msg)

            # Also publish TF transform for visualization
            self.publish_tf_transform(pose_matrix, timestamp)
            
        except Exception as e:
            rospy.logerr(f"Error publishing frame {frame_id}: {e}")
        
        self.current_frame += 1
    
    def publish_tf_transform(self, pose_matrix, timestamp):
        """发布TF变换"""
        try:
            import tf.transformations as tf_trans
            
            # Extract translation and rotation from pose matrix
            translation = tf_trans.translation_from_matrix(pose_matrix)
            quaternion = tf_trans.quaternion_from_matrix(pose_matrix)
            
            # Create transform message
            transform = TransformStamped()
            transform.header.stamp = timestamp
            transform.header.frame_id = "map"
            transform.child_frame_id = "camera_color_optical_frame"
            
            transform.transform.translation.x = translation[0]
            transform.transform.translation.y = translation[1]
            transform.transform.translation.z = translation[2]
            
            transform.transform.rotation.x = quaternion[0]
            transform.transform.rotation.y = quaternion[1]
            transform.transform.rotation.z = quaternion[2]
            transform.transform.rotation.w = quaternion[3]
            
            # Broadcast transform
            self.tf_broadcaster.sendTransform(transform)
            
        except Exception as e:
            rospy.logwarn(f"Failed to publish TF transform: {e}")

if __name__ == '__main__':
    try:
        publisher = MyDataPublisher()
        rospy.spin()
    except rospy.ROSInterruptException:
        pass
    except Exception as e:
        rospy.logerr(f"Publisher failed: {e}")
