#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
高级机器狗键盘控制节点
支持连续移动、组合动作和实时状态显示
"""

import rospy
import sys
import threading
import time
import math
from geometry_msgs.msg import Twist
from std_msgs.msg import String
from nav_msgs.msg import Odometry
import termios
import tty
import select

class AdvancedRobotController:
    def __init__(self):
        # 初始化ROS节点
        rospy.init_node('advanced_robot_controller', anonymous=True)
        
        # 发布者
        self.cmd_vel_pub = rospy.Publisher('/cmd_vel', Twist, queue_size=1)
        self.status_pub = rospy.Publisher('/robot_control_status', String, queue_size=1)
        
        # 订阅者（可选，用于获取机器人状态）
        self.odom_sub = rospy.Subscriber('/odom', Odometry, self.odom_callback, queue_size=1)
        
        # 控制参数
        self.max_linear_speed = rospy.get_param('~max_linear_speed', 1.0)
        self.max_angular_speed = rospy.get_param('~max_angular_speed', 2.0)
        self.acceleration = rospy.get_param('~acceleration', 0.5)
        self.publish_rate = rospy.get_param('~publish_rate', 10.0)  # Hz
        
        # 当前状态
        self.target_linear_x = 0.0
        self.target_linear_y = 0.0
        self.target_angular_z = 0.0
        self.current_linear_x = 0.0
        self.current_linear_y = 0.0
        self.current_angular_z = 0.0
        
        # 按键状态
        self.pressed_keys = set()
        self.key_lock = threading.Lock()
        
        # 机器人位置信息
        self.robot_x = 0.0
        self.robot_y = 0.0
        self.robot_yaw = 0.0
        
        # 控制标志
        self.running = True
        self.emergency_stop = False
        
        # 保存终端设置
        self.settings = termios.tcgetattr(sys.stdin)
        
        # 启动控制线程
        self.control_thread = threading.Thread(target=self.control_loop)
        self.control_thread.daemon = True
        self.control_thread.start()
        
        # 启动状态显示线程
        self.display_thread = threading.Thread(target=self.display_loop)
        self.display_thread.daemon = True
        self.display_thread.start()
        
        self.print_instructions()
    
    def odom_callback(self, msg):
        """里程计回调函数"""
        self.robot_x = msg.pose.pose.position.x
        self.robot_y = msg.pose.pose.position.y
        # 简化的yaw角度计算
        orientation = msg.pose.pose.orientation
        self.robot_yaw = 2 * math.atan2(orientation.z, orientation.w)
    
    def print_instructions(self):
        """打印控制说明"""
        instructions = """
╔══════════════════════════════════════════════════════════════╗
║                    高级机器狗键盘控制                          ║
╠══════════════════════════════════════════════════════════════╣
║ 移动控制 (可组合使用):                                        ║
║   W/S  : 前进/后退          A/D  : 左转/右转                  ║
║   Q/E  : 左平移/右平移      Z/C  : 原地左转/右转              ║
║                                                              ║
║ 速度控制:                                                    ║
║   R/F  : 增加/减少最大线速度                                  ║
║   T/G  : 增加/减少最大角速度                                  ║
║                                                              ║
║ 特殊功能:                                                    ║
║   空格  : 紧急停止          H : 显示帮助                      ║
║   P    : 暂停/恢复          X : 退出程序                      ║
║   I    : 显示机器人状态                                       ║
╚══════════════════════════════════════════════════════════════╝

按任意键开始控制...
        """
        print(instructions)
    
    def get_key_non_blocking(self):
        """非阻塞获取键盘输入"""
        if select.select([sys.stdin], [], [], 0) == ([sys.stdin], [], []):
            return sys.stdin.read(1)
        return None
    
    def update_targets_from_keys(self):
        """根据按键状态更新目标速度"""
        with self.key_lock:
            # 重置目标速度
            self.target_linear_x = 0.0
            self.target_linear_y = 0.0
            self.target_angular_z = 0.0
            
            # 线性运动
            if 'w' in self.pressed_keys:
                self.target_linear_x += self.max_linear_speed
            if 's' in self.pressed_keys:
                self.target_linear_x -= self.max_linear_speed
            if 'q' in self.pressed_keys:
                self.target_linear_y += self.max_linear_speed
            if 'e' in self.pressed_keys:
                self.target_linear_y -= self.max_linear_speed
            
            # 角速度运动
            if 'a' in self.pressed_keys:
                self.target_angular_z += self.max_angular_speed
            if 'd' in self.pressed_keys:
                self.target_angular_z -= self.max_angular_speed
            if 'z' in self.pressed_keys:
                self.target_angular_z += self.max_angular_speed * 0.5
            if 'c' in self.pressed_keys:
                self.target_angular_z -= self.max_angular_speed * 0.5
    
    def smooth_velocity_update(self, dt):
        """平滑速度更新"""
        if self.emergency_stop:
            self.current_linear_x = 0.0
            self.current_linear_y = 0.0
            self.current_angular_z = 0.0
            return
        
        # 计算速度变化量
        accel_step = self.acceleration * dt
        
        # 平滑更新线速度
        if abs(self.target_linear_x - self.current_linear_x) > accel_step:
            if self.target_linear_x > self.current_linear_x:
                self.current_linear_x += accel_step
            else:
                self.current_linear_x -= accel_step
        else:
            self.current_linear_x = self.target_linear_x
        
        if abs(self.target_linear_y - self.current_linear_y) > accel_step:
            if self.target_linear_y > self.current_linear_y:
                self.current_linear_y += accel_step
            else:
                self.current_linear_y -= accel_step
        else:
            self.current_linear_y = self.target_linear_y
        
        # 平滑更新角速度
        if abs(self.target_angular_z - self.current_angular_z) > accel_step:
            if self.target_angular_z > self.current_angular_z:
                self.current_angular_z += accel_step
            else:
                self.current_angular_z -= accel_step
        else:
            self.current_angular_z = self.target_angular_z
    
    def control_loop(self):
        """主控制循环"""
        rate = rospy.Rate(self.publish_rate)
        last_time = time.time()
        
        while self.running and not rospy.is_shutdown():
            current_time = time.time()
            dt = current_time - last_time
            last_time = current_time
            
            # 更新目标速度
            self.update_targets_from_keys()
            
            # 平滑速度更新
            self.smooth_velocity_update(dt)
            
            # 发布控制命令
            twist = Twist()
            twist.linear.x = self.current_linear_x
            twist.linear.y = self.current_linear_y
            twist.angular.z = self.current_angular_z
            
            self.cmd_vel_pub.publish(twist)
            
            # 发布状态
            status = f"Linear:({self.current_linear_x:.2f},{self.current_linear_y:.2f}) Angular:{self.current_angular_z:.2f}"
            self.status_pub.publish(String(data=status))
            
            rate.sleep()
    
    def display_loop(self):
        """状态显示循环"""
        while self.running:
            # 清屏并显示状态
            print("\033[2J\033[H", end="")  # 清屏
            print("╔══════════════════════════════════════════════════════════════╗")
            print("║                    机器狗实时控制状态                          ║")
            print("╠══════════════════════════════════════════════════════════════╣")
            print(f"║ 当前速度: Linear X: {self.current_linear_x:6.2f} m/s                    ║")
            print(f"║          Linear Y: {self.current_linear_y:6.2f} m/s                    ║")
            print(f"║          Angular:  {self.current_angular_z:6.2f} rad/s                 ║")
            print("╠══════════════════════════════════════════════════════════════╣")
            print(f"║ 最大速度: Linear: {self.max_linear_speed:6.2f} m/s  Angular: {self.max_angular_speed:6.2f} rad/s ║")
            print(f"║ 机器人位置: X: {self.robot_x:6.2f} Y: {self.robot_y:6.2f} Yaw: {self.robot_yaw:6.2f}    ║")
            print("╠══════════════════════════════════════════════════════════════╣")
            
            # 显示当前按键
            with self.key_lock:
                keys_str = ', '.join(sorted(self.pressed_keys)) if self.pressed_keys else "无"
            print(f"║ 当前按键: {keys_str:<48} ║")
            
            if self.emergency_stop:
                print("║                    ⚠️  紧急停止激活  ⚠️                        ║")
            
            print("╚══════════════════════════════════════════════════════════════╝")
            print("按 H 显示帮助, 按 X 退出")
            
            time.sleep(0.1)
    
    def run(self):
        """主运行函数"""
        tty.setraw(sys.stdin.fileno())
        
        try:
            while self.running and not rospy.is_shutdown():
                key = self.get_key_non_blocking()
                
                if key:
                    key = key.lower()
                    
                    # 移动按键
                    if key in ['w', 's', 'a', 'd', 'q', 'e', 'z', 'c']:
                        with self.key_lock:
                            self.pressed_keys.add(key)
                    
                    # 速度调节
                    elif key == 'r':
                        self.max_linear_speed = min(self.max_linear_speed + 0.1, 2.0)
                    elif key == 'f':
                        self.max_linear_speed = max(self.max_linear_speed - 0.1, 0.1)
                    elif key == 't':
                        self.max_angular_speed = min(self.max_angular_speed + 0.1, 3.0)
                    elif key == 'g':
                        self.max_angular_speed = max(self.max_angular_speed - 0.1, 0.1)
                    
                    # 特殊功能
                    elif key == ' ':  # 紧急停止
                        self.emergency_stop = not self.emergency_stop
                        with self.key_lock:
                            self.pressed_keys.clear()
                    elif key == 'p':  # 暂停/恢复
                        with self.key_lock:
                            self.pressed_keys.clear()
                    elif key == 'h':  # 帮助
                        self.print_instructions()
                    elif key == 'x':  # 退出
                        self.running = False
                        break
                
                # 检查按键释放（简化处理）
                time.sleep(0.05)
                
        except KeyboardInterrupt:
            print("\n程序被用户中断")
        finally:
            self.running = False
            termios.tcsetattr(sys.stdin, termios.TCSADRAIN, self.settings)
            # 发送停止命令
            self.cmd_vel_pub.publish(Twist())

def main():
    try:
        controller = AdvancedRobotController()
        controller.run()
    except Exception as e:
        print(f"启动失败: {e}")

if __name__ == '__main__':
    main()
