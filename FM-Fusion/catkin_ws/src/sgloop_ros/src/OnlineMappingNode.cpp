#include <iostream>
#include <memory>
#include <sys/stat.h>
#include <vector>
#include <fstream>
#include <signal.h>

#include <ros/ros.h>
#include <tf/transform_broadcaster.h>
#include "tf/transform_listener.h"
#include <sensor_msgs/Image.h>
#include <geometry_msgs/Pose.h>
#include <cv_bridge/cv_bridge.h>

#include <open3d/Open3D.h>
#include <opencv2/opencv.hpp>
#include <json/json.h>

#include "tools/Utility.h"
#include "tools/IO.h"
#include "tools/TicToc.h"
#include "mapping/SemanticMapping.h"
#include "mapping/Detection.h"

#include "Visualization.h"
#include "sgloop_ros/SyncedFrame.h"

class OnlineMappingNode
{
private:
    ros::NodeHandle nh_;
    ros::NodeHandle nh_private_;
    ros::Subscriber synced_frame_sub_;
    
    // FM-Fusion components
    fmfusion::Config* global_config_;
    fmfusion::SemanticMapping* semantic_mapping_;
    Visualization::Visualizer viz_;
    
    // Parameters
    std::string config_file_;
    std::string output_folder_;
    std::string LOCAL_AGENT_;
    int frame_gap_;
    int max_frames_;
    bool debug_;
    
    // State
    int frame_count_;
    int prev_frame_id_;
    fmfusion::TicTocSequence tic_toc_seq_;
    
public:
    OnlineMappingNode(ros::NodeHandle& nh, ros::NodeHandle& nh_private) 
        : nh_(nh), nh_private_(nh_private), viz_(nh, nh_private), 
          frame_count_(0), prev_frame_id_(-100),
          tic_toc_seq_("# Online Mapping", 3)
    {
        // 获取参数
        if (!nh_private_.getParam("cfg_file", config_file_)) {
            config_file_ = "config/your_camera.yaml";
        }
        
        nh_private_.getParam("local_agent", LOCAL_AGENT_);
        frame_gap_ = nh_private_.param("frame_gap", 1);
        output_folder_ = nh_private_.param("output_folder", std::string(""));
        max_frames_ = nh_private_.param("max_frames", 5000);
        debug_ = nh_private_.param("debug", false);
        
        ROS_INFO("OnlineMappingNode started with config: %s", config_file_.c_str());
        
        // 使用修复后的配置文件进行FM-Fusion初始化
        ROS_INFO("Starting FM-Fusion initialization with fixed config...");
        initializeFMFusionGradual();
        
        // 订阅同步帧数据
        // 获取队列大小参数，实时数据建议使用较小的队列
        int queue_size = 1;  // 实时处理建议使用1
        nh_private_.param("subscriber_queue_size", queue_size, 1);

        synced_frame_sub_ = nh_.subscribe("/sync/output", queue_size,
                                         &OnlineMappingNode::syncedFrameCallback, this);
        
        ROS_INFO("OnlineMappingNode initialized successfully");
    }
    
    ~OnlineMappingNode()
    {
        if (semantic_mapping_) {
            delete semantic_mapping_;
        }
        if (global_config_) {
            delete global_config_;
        }
    }
    
private:
    void initializeFMFusion()
    {
        ROS_INFO("Starting FM-Fusion initialization...");

        // 加载配置 - 使用与MappingNode完全相同的方式
        ROS_INFO("Loading config file: %s", config_file_.c_str());
        try {
            global_config_ = fmfusion::utility::create_scene_graph_config(config_file_, true);
            if (!global_config_) {
                ROS_ERROR("Failed to load config file: %s", config_file_.c_str());
                return;
            }
            ROS_INFO("Config loaded successfully");

            // 设置Open3D详细级别 - 与MappingNode相同
            open3d::utility::SetVerbosityLevel((open3d::utility::VerbosityLevel)2);
            ROS_INFO("Open3D verbosity level set");

            // 设置输出目录 - 与MappingNode相同的逻辑
            if (output_folder_.size() > 0 && !open3d::utility::filesystem::DirectoryExists(output_folder_)) {
                open3d::utility::filesystem::MakeDirectory(output_folder_);
                ROS_INFO("Created output directory: %s", output_folder_.c_str());
            }

            // 写入配置文件 - 与MappingNode相同
            if (!output_folder_.empty()) {
                std::ofstream out_file(output_folder_ + "/config.txt");
                out_file << fmfusion::utility::config_to_message(*global_config_);
                out_file.close();
                ROS_INFO("Config file written to output directory");
            }

            // 初始化语义建图 - 使用与MappingNode完全相同的方式
            ROS_INFO("Initializing SemanticMapping...");
            semantic_mapping_ = new fmfusion::SemanticMapping(global_config_->mapping_cfg,
                                                              global_config_->instance_cfg);
            ROS_INFO("SemanticMapping initialized");

            ROS_INFO("FM-Fusion initialized successfully");
        } catch (const std::exception& e) {
            ROS_ERROR("Exception during FM-Fusion initialization: %s", e.what());
            throw;
        }
    }
    
    void syncedFrameCallback(const sgloop_ros::SyncedFrame::ConstPtr& msg)
    {
        if (frame_count_ >= max_frames_) {
            // 如果是第一次达到最大帧数，保存结果
            static bool results_saved = false;
            if (!results_saved) {
                ROS_INFO("Reached max_frames (%d), saving results...", max_frames_);
                saveResults();
                results_saved = true;
            }
            return;
        }

        if ((frame_count_ - prev_frame_id_) < frame_gap_) {
            frame_count_++;
            return;
        }

        ROS_INFO("Processing frame %d...", frame_count_);
        tic_toc_seq_.tic();

        // 转换ROS图像消息到Open3D格式
        open3d::geometry::Image color, depth;

        // 转换RGB图像
        cv_bridge::CvImagePtr cv_rgb;
        try {
            cv_rgb = cv_bridge::toCvCopy(msg->rgb, sensor_msgs::image_encodings::BGR8);
            // 转换BGR到RGB
            cv::Mat rgb_mat;
            cv::cvtColor(cv_rgb->image, rgb_mat, cv::COLOR_BGR2RGB);

            // 转换为Open3D格式
            color.Prepare(rgb_mat.cols, rgb_mat.rows, 3, 1);
            memcpy(color.data_.data(), rgb_mat.data, rgb_mat.total() * rgb_mat.elemSize());
        } catch (cv_bridge::Exception& e) {
            ROS_ERROR("cv_bridge exception for RGB: %s", e.what());
            return;
        }

        // 转换深度图像
        cv_bridge::CvImagePtr cv_depth;
        try {
            cv_depth = cv_bridge::toCvCopy(msg->depth, sensor_msgs::image_encodings::TYPE_16UC1);

            // 转换为Open3D格式
            depth.Prepare(cv_depth->image.cols, cv_depth->image.rows, 1, 2);
            memcpy(depth.data_.data(), cv_depth->image.data, cv_depth->image.total() * cv_depth->image.elemSize());
        } catch (cv_bridge::Exception& e) {
            ROS_ERROR("cv_bridge exception for depth: %s", e.what());
            return;
        }

        // 创建RGBD图像
        auto rgbd = open3d::geometry::RGBDImage::CreateFromColorAndDepth(
            color, depth,
            global_config_->mapping_cfg.depth_scale,
            global_config_->mapping_cfg.depth_max,
            false);

        tic_toc_seq_.toc();

        // 解析检测数据
        ROS_INFO("=== Frame %d JSON Data Debug ===", frame_count_);
        ROS_INFO("JSON length: %zu characters", msg->json.length());
        ROS_INFO("JSON first 200 chars: %s", msg->json.substr(0, 200).c_str());
        ROS_INFO("Mask image size: %dx%d, encoding: %s",
                 msg->mask.width, msg->mask.height, msg->mask.encoding.c_str());

        std::vector<fmfusion::DetectionPtr> detections;
        bool loaded = parseDetectionsFromJson(msg->json, msg->mask, detections);

        ROS_INFO("JSON parsing result: %s, detections count: %zu",
                 loaded ? "SUCCESS" : "FAILED", detections.size());

        if (!loaded) {
            ROS_WARN("Failed to parse detections from JSON for frame %d", frame_count_);
            frame_count_++;
            return;
        }

        // 转换位姿
        Eigen::Matrix4d pose = transformMatrixToPose(msg->transform_matrix);

        // 调试信息
        ROS_INFO("Frame %d: RGBD size: %dx%d, detections: %zu",
                 frame_count_, rgbd->color_.width_, rgbd->color_.height_, detections.size());

        // 检查深度数据质量
        int valid_depth_count = 0;
        int total_pixels = rgbd->depth_.width_ * rgbd->depth_.height_;
        for (int v = 0; v < rgbd->depth_.height_; v++) {
            for (int u = 0; u < rgbd->depth_.width_; u++) {
                float depth_val = *rgbd->depth_.PointerAt<float>(u, v);
                if (depth_val > 0.0 && depth_val < 10.0) {  // 合理的深度范围
                    valid_depth_count++;
                }
            }
        }
        ROS_INFO("Depth quality: %d/%d valid pixels (%.1f%%)",
                 valid_depth_count, total_pixels, 100.0 * valid_depth_count / total_pixels);

        // 检查每个检测的掩码大小
        for (size_t i = 0; i < detections.size(); i++) {
            int mask_pixels = cv::countNonZero(detections[i]->instances_idxs_);
            ROS_INFO("Detection %zu (%s): mask has %d pixels",
                     i, detections[i]->extract_label_string().c_str(), mask_pixels);
        }
        ROS_INFO("Pose matrix:\n[%.3f %.3f %.3f %.3f]\n[%.3f %.3f %.3f %.3f]\n[%.3f %.3f %.3f %.3f]\n[%.3f %.3f %.3f %.3f]",
                 pose(0,0), pose(0,1), pose(0,2), pose(0,3),
                 pose(1,0), pose(1,1), pose(1,2), pose(1,3),
                 pose(2,0), pose(2,1), pose(2,2), pose(2,3),
                 pose(3,0), pose(3,1), pose(3,2), pose(3,3));

        // 进行语义建图
        ROS_INFO("Calling semantic_mapping_->integrate...");
        semantic_mapping_->integrate(frame_count_, rgbd, pose, detections);
        ROS_INFO("integrate() completed");
        tic_toc_seq_.toc();

        // 可视化
        visualizeResults(pose, color, detections);

        prev_frame_id_ = frame_count_;
        frame_count_++;

        tic_toc_seq_.toc();

        ROS_INFO("Frame %d processed successfully", frame_count_ - 1);
    }

    void initializeFMFusionGradual()
    {
        ROS_INFO("Step 0: Starting initialization");
        ROS_INFO("Step 0.1: config_file_ length: %zu", config_file_.length());

        if (config_file_.empty()) {
            ROS_ERROR("Config file path is empty!");
            return;
        }

        ROS_INFO("Step 1: Loading config file: %s", config_file_.c_str());

        // 添加文件存在性检查
        ROS_INFO("Step 1.0: Checking file existence...");
        std::ifstream test_file(config_file_);
        if (!test_file.good()) {
            ROS_ERROR("Config file does not exist or cannot be read: %s", config_file_.c_str());
            return;
        }
        test_file.close();
        ROS_INFO("Step 1.0: File check passed");
        
        try {
            ROS_INFO("Step 1.1: Calling create_scene_graph_config...");
            global_config_ = fmfusion::utility::create_scene_graph_config(config_file_, true);
            ROS_INFO("Step 1.2: create_scene_graph_config returned successfully");
            
            if (!global_config_) {
                ROS_ERROR("Failed to load config file: %s", config_file_.c_str());
                return;
            }
            ROS_INFO("Step 1: Config loaded successfully");

            ROS_INFO("Step 2: Setting Open3D verbosity level");
            open3d::utility::SetVerbosityLevel((open3d::utility::VerbosityLevel)2);
            ROS_INFO("Step 2: Open3D verbosity level set");

            ROS_INFO("Step 3: Creating output directory");
            if (output_folder_.size() > 0 && !open3d::utility::filesystem::DirectoryExists(output_folder_)) {
                open3d::utility::filesystem::MakeDirectory(output_folder_);
                ROS_INFO("Step 3: Created output directory: %s", output_folder_.c_str());
            }

            ROS_INFO("Step 4: Writing config file");
            if (!output_folder_.empty()) {
                std::ofstream out_file(output_folder_ + "/config.txt");
                out_file << fmfusion::utility::config_to_message(*global_config_);
                out_file.close();
                ROS_INFO("Step 4: Config file written to output directory");
            }

            ROS_INFO("Step 5: Initializing SemanticMapping");
            semantic_mapping_ = new fmfusion::SemanticMapping(global_config_->mapping_cfg,
                                                              global_config_->instance_cfg);
            ROS_INFO("Step 5: SemanticMapping initialized successfully");

            ROS_INFO("FM-Fusion gradual initialization completed successfully");
        } catch (const std::exception& e) {
            ROS_ERROR("Exception during gradual FM-Fusion initialization: %s", e.what());
            throw;
        }
    }

    bool parseDetectionsFromJson(const std::string& json_str,
                                const sensor_msgs::Image& mask_msg,
                                std::vector<fmfusion::DetectionPtr>& detections)
    {
        detections.clear();

        ROS_INFO("=== parseDetectionsFromJson Debug ===");
        ROS_INFO("JSON string length: %zu", json_str.length());
        ROS_INFO("JSON string (first 500 chars): %s", json_str.substr(0, 500).c_str());

        if (json_str.empty()) {
            ROS_WARN("JSON string is empty, returning true with no detections");
            return true; // 允许空检测
        }

        try {
            Json::Value root;
            Json::Reader reader;
            if (!reader.parse(json_str, root)) {
                ROS_ERROR("Failed to parse JSON: %s", reader.getFormattedErrorMessages().c_str());
                return false;
            }

            ROS_INFO("JSON parsed successfully, root type: %d", root.type());

            // 检查JSON结构
            ROS_INFO("JSON root members:");
            for (const auto& member : root.getMemberNames()) {
                ROS_INFO("  - %s (type: %d)", member.c_str(), root[member].type());
            }

            // 解析newdata.bag中的JSON格式
            if (!root.isMember("detections") || !root["detections"].isArray()) {
                ROS_ERROR("JSON does not contain valid 'detections' array");
                ROS_ERROR("Available members: %s", Json::writeString(Json::StreamWriterBuilder(), root).c_str());
                return false;
            }

            const Json::Value& detections_array = root["detections"];
            ROS_INFO("Found detections array with %d elements", detections_array.size());

            if (detections_array.size() < 1) {
                ROS_WARN("No detections found in JSON.");
                return true; // 返回true但检测为空
            }

            // 获取掩码图像
            cv_bridge::CvImagePtr cv_mask;
            try {
                // 先尝试直接转换，如果失败则转换编码
                if (mask_msg.encoding == sensor_msgs::image_encodings::MONO8) {
                    cv_mask = cv_bridge::toCvCopy(mask_msg, sensor_msgs::image_encodings::MONO8);
                } else {
                    // 如果是BGR8或其他格式，提取第一个通道作为ID图像
                    cv_bridge::CvImagePtr cv_temp = cv_bridge::toCvCopy(mask_msg);
                    cv_mask = boost::make_shared<cv_bridge::CvImage>();
                    cv_mask->header = cv_temp->header;
                    cv_mask->encoding = sensor_msgs::image_encodings::MONO8;

                    if (cv_temp->image.channels() == 3) {
                        // 对于BGR图像，需要从颜色值映射到检测ID
                        // 注意：OpenCV中BGR顺序是[B,G,R]，第一个通道是B通道
                        std::vector<cv::Mat> channels;
                        cv::split(cv_temp->image, channels);
                        cv::Mat b_channel = channels[0].clone(); // 使用B通道作为原始颜色值

                        // 创建映射后的ID图像
                        cv_mask->image = cv::Mat::zeros(b_channel.size(), CV_8UC1);

                        ROS_INFO("Converting BGR mask to ID mask using B channel mapping");
                    } else if (cv_temp->image.channels() == 1) {
                        cv_mask->image = cv_temp->image.clone();
                    } else {
                        ROS_ERROR("Unsupported mask image channels: %d", cv_temp->image.channels());
                        return false;
                    }
                }
                ROS_INFO("Mask image converted successfully: %dx%d, unique values: %d",
                         cv_mask->image.cols, cv_mask->image.rows,
                         cv::countNonZero(cv_mask->image != cv_mask->image.at<uchar>(0,0)));
            } catch (cv_bridge::Exception& e) {
                ROS_ERROR("cv_bridge exception for mask: %s", e.what());
                return false;
            }

            // 首先建立BGR颜色值到检测ID的映射（如果是BGR图像）
            std::map<uchar, int> color_to_id_map;
            cv::Mat original_b_channel;
            bool is_bgr_mask = false;

            if (cv_mask && !cv_mask->image.empty()) {
                // 检查是否是从BGR转换来的（通过检查是否有多个通道的原始图像）
                cv_bridge::CvImagePtr cv_temp = cv_bridge::toCvCopy(mask_msg);
                if (cv_temp->image.channels() == 3) {
                    is_bgr_mask = true;
                    std::vector<cv::Mat> channels;
                    cv::split(cv_temp->image, channels);
                    original_b_channel = channels[0].clone();
                    ROS_INFO("Building color-to-ID mapping for BGR mask");
                }
            }

            // 解析每个检测对象 - 基于newdata.bag的实际格式
            for (int i = 0; i < detections_array.size(); ++i) {
                const Json::Value& detection_json = detections_array[i];
                int detection_id = detection_json["value"].asInt();

                if (detection_id == 0) continue; // 跳过背景

                auto detection = std::make_shared<fmfusion::Detection>(detection_id);

                // 解析标签和分数 - newdata.bag格式
                if (detection_json.isMember("label")) {
                    std::string label = detection_json["label"].asString();
                    float score = 1.0f; // 默认分数

                    if (detection_json.isMember("logit")) {
                        score = detection_json["logit"].asFloat();
                    }

                    detection->labels_.push_back(std::make_pair(label, score));
                }

                // 解析边界框 - newdata.bag格式
                if (detection_json.isMember("box") && detection_json["box"].isArray() &&
                    detection_json["box"].size() == 4) {
                    const Json::Value& box = detection_json["box"];
                    detection->bbox_.u0 = box[0].asDouble();
                    detection->bbox_.v0 = box[1].asDouble();
                    detection->bbox_.u1 = box[2].asDouble();
                    detection->bbox_.v1 = box[3].asDouble();
                } else {
                    // 如果没有边界框信息，设置默认值
                    detection->bbox_.u0 = 0;
                    detection->bbox_.v0 = 0;
                    detection->bbox_.u1 = cv_mask->image.cols;
                    detection->bbox_.v1 = cv_mask->image.rows;
                }

                // 从掩码图像中提取对应的实例掩码（如果有掩码图像）
                if (cv_mask && !cv_mask->image.empty()) {
                    // 调试：检查掩码图像中的实际值
                    cv::Mat mask_img = cv_mask->image;
                    std::vector<uchar> unique_values;
                    for (int v = 0; v < mask_img.rows; v++) {
                        for (int u = 0; u < mask_img.cols; u++) {
                            uchar val = mask_img.at<uchar>(v, u);
                            if (std::find(unique_values.begin(), unique_values.end(), val) == unique_values.end()) {
                                unique_values.push_back(val);
                                if (unique_values.size() > 20) break; // 限制输出数量
                            }
                        }
                        if (unique_values.size() > 20) break;
                    }

                    ROS_INFO("Mask unique values (first 20): ");
                    for (size_t k = 0; k < std::min(unique_values.size(), size_t(20)); k++) {
                        ROS_INFO("  %d", (int)unique_values[k]);
                    }
                    ROS_INFO("Looking for detection_id: %d", detection_id);

                    cv::Mat instance_mask;
                    if (is_bgr_mask && !original_b_channel.empty()) {
                        // 对于BGR掩码，使用智能颜色映射策略
                        // 首先尝试直接匹配detection_id
                        instance_mask = (original_b_channel == detection_id);
                        int direct_pixels = cv::countNonZero(instance_mask);

                        if (direct_pixels == 0) {
                            // 如果直接匹配失败，尝试查找最接近的颜色值
                            // 获取B通道中的所有唯一值
                            std::set<uchar> unique_values;
                            for (int v = 0; v < original_b_channel.rows; v++) {
                                for (int u = 0; u < original_b_channel.cols; u++) {
                                    uchar val = original_b_channel.at<uchar>(v, u);
                                    if (val != 0) { // 忽略背景
                                        unique_values.insert(val);
                                    }
                                }
                            }

                            // 将唯一值转换为向量并排序
                            std::vector<uchar> sorted_values(unique_values.begin(), unique_values.end());

                            // 如果detection_id在范围内，使用对应的颜色值
                            if (detection_id > 0 && detection_id <= sorted_values.size()) {
                                uchar target_color = sorted_values[detection_id - 1];
                                instance_mask = (original_b_channel == target_color);
                                ROS_INFO("Using color mapping: detection_id %d -> color %d", detection_id, (int)target_color);
                            } else {
                                instance_mask = cv::Mat::zeros(original_b_channel.size(), CV_8UC1);
                            }
                        }

                        // 同时更新映射后的ID图像
                        cv_mask->image.setTo(detection_id, instance_mask);

                        int mask_pixels = cv::countNonZero(instance_mask);
                        ROS_INFO("Found %d pixels for detection_id %d (BGR mapping)", mask_pixels, detection_id);
                    } else {
                        // 对于已经是ID格式的掩码，直接比较
                        instance_mask = (cv_mask->image == detection_id);
                        int mask_pixels = cv::countNonZero(instance_mask);
                        ROS_INFO("Found %d pixels for detection_id %d (direct)", mask_pixels, detection_id);
                    }

                    // 确保掩码值为255而不是1
                    cv::Mat final_mask;
                    instance_mask.convertTo(final_mask, CV_8UC1, 255);
                    detection->instances_idxs_ = final_mask;

                    // 如果JSON中没有边界框信息，从掩码中计算
                    if (!detection_json.isMember("box")) {
                        std::vector<std::vector<cv::Point>> contours;
                        cv::findContours(instance_mask, contours, cv::RETR_EXTERNAL, cv::CHAIN_APPROX_SIMPLE);

                        if (!contours.empty()) {
                            cv::Rect bbox = cv::boundingRect(contours[0]);
                            for (size_t j = 1; j < contours.size(); ++j) {
                                bbox |= cv::boundingRect(contours[j]);
                            }

                            detection->bbox_.u0 = bbox.x;
                            detection->bbox_.v0 = bbox.y;
                            detection->bbox_.u1 = bbox.x + bbox.width;
                            detection->bbox_.v1 = bbox.y + bbox.height;
                        }
                    }
                } else {
                    // 如果没有掩码图像，创建基于边界框的简单掩码
                    if (detection_json.isMember("box")) {
                        cv::Mat simple_mask = cv::Mat::zeros(480, 640, CV_8UC1); // 假设图像尺寸
                        cv::Rect roi(detection->bbox_.u0, detection->bbox_.v0,
                                   detection->bbox_.u1 - detection->bbox_.u0,
                                   detection->bbox_.v1 - detection->bbox_.v0);
                        simple_mask(roi) = 255;
                        detection->instances_idxs_ = simple_mask;
                    }
                }

                detections.push_back(detection);
            }

            ROS_INFO("Parsed %zu detections from JSON", detections.size());
            return true;

        } catch (const std::exception& e) {
            ROS_ERROR("Exception parsing JSON: %s", e.what());
            return false;
        }
    }
    
    template<typename T>
    Eigen::Matrix4d transformMatrixToPose(const T& transform_array)
    {
        Eigen::Matrix4d matrix = Eigen::Matrix4d::Identity();

        if (transform_array.size() == 16) {
            // 将数组转换为4x4矩阵
            for (int i = 0; i < 4; i++) {
                for (int j = 0; j < 4; j++) {
                    matrix(i, j) = transform_array[i * 4 + j];
                }
            }
        } else {
            ROS_WARN("Invalid transform matrix size: %zu, expected 16", transform_array.size());
        }

        return matrix;
    }

    
    void visualizeResults(const Eigen::Matrix4d& pose, 
                         const open3d::geometry::Image& color,
                         const std::vector<fmfusion::DetectionPtr>& detections)
    {
        // 可视化相机位姿
        Visualization::render_camera_pose(pose, viz_.camera_pose, LOCAL_AGENT_, frame_count_);
        Visualization::render_path(pose, viz_.path_msg, viz_.path, LOCAL_AGENT_, frame_count_);
        
        // 可视化检测结果
        if (viz_.pred_image.getNumSubscribers() > 0) {
            Visualization::render_rgb_detections(color, detections, viz_.pred_image, LOCAL_AGENT_);
        }
        
        // 可视化3D语义地图
        Visualization::render_semantic_map(
            semantic_mapping_->export_global_pcd(true, 0.05),
            semantic_mapping_->export_instance_centroids(0, debug_),
            semantic_mapping_->export_instance_annotations(0),
            viz_,
            LOCAL_AGENT_);
    }
    
public:
    void saveResults()
    {
        if (output_folder_.empty()) {
            ROS_ERROR("Output folder is empty, cannot save results");
            return;
        }

        ROS_WARN("Starting to save results to %s", output_folder_.c_str());

        // 检查输出目录是否存在，如果不存在则创建
        std::string mkdir_cmd = "mkdir -p " + output_folder_;
        int result = system(mkdir_cmd.c_str());
        if (result != 0) {
            ROS_ERROR("Failed to create output directory: %s", output_folder_.c_str());
            return;
        }
        ROS_INFO("Output directory created/verified: %s", output_folder_.c_str());

        // 最终处理
        ROS_INFO("Extracting point cloud...");
        semantic_mapping_->extract_point_cloud();
        ROS_INFO("Merging floor instances...");
        semantic_mapping_->merge_floor(true);

        // 添加调试信息：检查实例状态
        ROS_INFO("=== Final Instance Status Debug ===");
        auto centroids = semantic_mapping_->export_instance_centroids(0, true);
        ROS_INFO("Total valid instances for export: %zu", centroids.size());

        // 保存结果
        std::string sequence_name = "online_mapping";
        std::string full_output_path = output_folder_ + "/" + sequence_name;

        ROS_WARN("Saving semantic mapping to: %s", full_output_path.c_str());

        // 创建完整的输出目录
        std::string mkdir_full_cmd = "mkdir -p " + full_output_path;
        int mkdir_result = system(mkdir_full_cmd.c_str());
        if (mkdir_result != 0) {
            ROS_ERROR("Failed to create full output directory: %s", full_output_path.c_str());
            return;
        }

        // 检查实例数量
        auto centroids_before_save = semantic_mapping_->export_instance_centroids(0, true);
        ROS_INFO("About to save %zu instances", centroids_before_save.size());

        try {
            semantic_mapping_->Save(full_output_path);
            ROS_INFO("semantic_mapping_->Save() completed");

            // 验证保存结果
            std::string check_cmd = "ls -la " + full_output_path + "/*.ply 2>/dev/null | wc -l";
            FILE* pipe = popen(check_cmd.c_str(), "r");
            if (pipe) {
                char buffer[128];
                std::string result = "";
                while (fgets(buffer, sizeof(buffer), pipe) != NULL) {
                    result += buffer;
                }
                pclose(pipe);
                int ply_count = std::stoi(result);
                ROS_INFO("Verification: Found %d PLY files in output directory", ply_count);
                if (ply_count == 0) {
                    ROS_ERROR("No PLY files were saved! Save operation may have failed silently.");
                }
            }
        } catch (const std::exception& e) {
            ROS_ERROR("Exception during Save(): %s", e.what());
        }

        ROS_INFO("Exporting time records...");
        tic_toc_seq_.export_data(full_output_path + "/time_records.txt");

        ROS_INFO("Writing config file...");
        fmfusion::utility::write_config(full_output_path + "/config.txt", *global_config_);

        ROS_WARN("Results saved successfully to: %s", full_output_path.c_str());
    }
};

int main(int argc, char **argv)
{
    ros::init(argc, argv, "online_mapping_node");
    ros::NodeHandle nh;
    ros::NodeHandle nh_private("~");
    
    OnlineMappingNode mapping_node(nh, nh_private);
    
    ROS_INFO("Online mapping node started. Waiting for synced frames...");
    
    // 设置关闭时保存结果的信号处理
    // ros::on_shutdown在某些版本中可能不可用，使用signal处理
    signal(SIGINT, [](int sig) {
        ROS_INFO("Received SIGINT, shutting down...");
        ros::shutdown();
    });
    
    ros::spin();
    
    return 0;
}
