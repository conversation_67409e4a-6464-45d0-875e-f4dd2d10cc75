#include <iostream>
#include <memory>
#include <sys/stat.h>
#include <vector>
#include <fstream>

#include <ros/ros.h>
#include <tf/transform_broadcaster.h>
#include "tf/transform_listener.h"
#include <tf2_ros/transform_listener.h>
#include <tf2_geometry_msgs/tf2_geometry_msgs.h>

#include <sensor_msgs/Image.h>
#include <sensor_msgs/CameraInfo.h>
#include <geometry_msgs/PoseStamped.h>
#include <nav_msgs/Odometry.h>
#include <std_msgs/String.h>
#include <cv_bridge/cv_bridge.h>


#include <open3d/Open3D.h>
#include <opencv2/opencv.hpp>
#include <jsoncpp/json/json.h>

#include "tools/Utility.h"
#include "tools/IO.h"
#include "tools/TicToc.h"
#include "mapping/SemanticMapping.h"

#include "Visualization.h"

using namespace fmfusion;

class OnlineMappingNodeV2
{
private:
    ros::NodeHandle nh_;
    ros::NodeHandle nh_private_;

    // Subscribers (no synchronization needed since messages are already synchronized)
    ros::Subscriber rgb_sub_;
    ros::Subscriber depth_sub_;
    ros::Subscriber detection_sub_;
    ros::Subscriber mask_sub_;
    ros::Subscriber pose_sub_;

    // Data buffers for synchronized messages
    sensor_msgs::Image::ConstPtr latest_rgb_;
    sensor_msgs::Image::ConstPtr latest_depth_;
    std::string latest_detection_json_;
    sensor_msgs::Image::ConstPtr latest_mask_;
    nav_msgs::Odometry::ConstPtr latest_pose_;
    bool has_all_data_;

    // TF
    tf::TransformListener tf_listener_;

    // FM-Fusion components
    Config* global_config_;
    SemanticMapping* semantic_mapping_;
    Visualization::Visualizer viz_;

    // Parameters
    std::string config_file_;
    std::string output_folder_;
    std::string LOCAL_AGENT_;
    std::string camera_frame_;
    std::string world_frame_;
    int frame_gap_;
    int max_frames_;
    bool debug_;

    // State
    int frame_count_;
    int prev_frame_id_;
    TicTocSequence tic_toc_seq_;

public:
    OnlineMappingNodeV2(ros::NodeHandle& nh, ros::NodeHandle& nh_private)
        : nh_(nh), nh_private_(nh_private), viz_(nh, nh_private),
          frame_count_(0), prev_frame_id_(-100),
          tic_toc_seq_("# Online Mapping V2", 3), has_all_data_(false)
    {
        // Get parameters
        if (!nh_private_.getParam("cfg_file", config_file_)) {
            config_file_ = "config/online.yaml";
        }

        nh_private_.getParam("local_agent", LOCAL_AGENT_);
        frame_gap_ = nh_private_.param("frame_gap", 1);
        output_folder_ = nh_private_.param("output_folder", std::string(""));
        max_frames_ = nh_private_.param("max_frames", 1000);
        debug_ = nh_private_.param("debug", false);
        camera_frame_ = nh_private_.param("camera_frame", std::string("camera_color_optical_frame"));
        world_frame_ = nh_private_.param("world_frame", std::string("map"));

        ROS_INFO("OnlineMappingNodeV2 started with config: %s", config_file_.c_str());

        // Initialize FM-Fusion
        initializeFMFusion();

        // Setup subscribers
        setupSubscribers();

        ROS_INFO("OnlineMappingNodeV2 initialized successfully");
    }

    void initializeFMFusion()
    {
        try {
            ROS_INFO("Loading config file: %s", config_file_.c_str());
            global_config_ = utility::create_scene_graph_config(config_file_, true);
            if (!global_config_) {
                ROS_ERROR("Failed to load config file: %s", config_file_.c_str());
                return;
            }

            open3d::utility::SetVerbosityLevel((open3d::utility::VerbosityLevel)2);

            if (output_folder_.size() > 0 && !open3d::utility::filesystem::DirectoryExists(output_folder_)) {
                open3d::utility::filesystem::MakeDirectory(output_folder_);
            }

            if (!output_folder_.empty()) {
                std::ofstream out_file(output_folder_ + "/config.txt");
                out_file << utility::config_to_message(*global_config_);
                out_file.close();
            }

            semantic_mapping_ = new SemanticMapping(global_config_->mapping_cfg, global_config_->instance_cfg);

            ROS_INFO("FM-Fusion initialized successfully");
        } catch (const std::exception& e) {
            ROS_ERROR("Exception during FM-Fusion initialization: %s", e.what());
            throw;
        }
    }

    void setupSubscribers()
    {
        // 使用小队列避免数据积压
        rgb_sub_ = nh_.subscribe("/sync/rgb", 1, &OnlineMappingNodeV2::rgbCallback, this);
        depth_sub_ = nh_.subscribe("/sync/depth", 1, &OnlineMappingNodeV2::depthCallback, this);
        detection_sub_ = nh_.subscribe("/sync/json", 1, &OnlineMappingNodeV2::detectionCallback, this);
        mask_sub_ = nh_.subscribe("/sync/mask", 1, &OnlineMappingNodeV2::maskCallback, this);
        pose_sub_ = nh_.subscribe("/sync/pose", 1, &OnlineMappingNodeV2::poseCallback, this);

        ROS_INFO("Subscribers setup complete with queue_size=1 with queue_size=1");
    }

    void rgbCallback(const sensor_msgs::Image::ConstPtr& msg)
    {
        latest_rgb_ = msg;
        checkAndProcessFrame();  // 改为智能检查
    }

    void depthCallback(const sensor_msgs::Image::ConstPtr& msg)
    {
        latest_depth_ = msg;
        checkAndProcessFrame();
    }

    void detectionCallback(const std_msgs::String::ConstPtr& msg)
    {
        latest_detection_json_ = msg->data;
        checkAndProcessFrame();
    }

    void maskCallback(const sensor_msgs::Image::ConstPtr& msg)
    {
        latest_mask_ = msg;
        checkAndProcessFrame();
    }

    void poseCallback(const nav_msgs::Odometry::ConstPtr& msg)
    {
        latest_pose_ = msg;
        checkAndProcessFrame();
    }

    void checkAndProcessFrame()
    {
        // 只有当所有数据都更新时才处理
        if (!latest_rgb_ || !latest_depth_ || latest_detection_json_.empty() || 
            !latest_mask_ || !latest_pose_) {
            return;
        }
        
        // 检查时间戳是否匹配（允许小误差）
        ros::Time rgb_time = latest_rgb_->header.stamp;
        ros::Time depth_time = latest_depth_->header.stamp;
        ros::Time mask_time = latest_mask_->header.stamp;
        ros::Time pose_time = latest_pose_->header.stamp;
        
        double max_time_diff = 0.7;  // 50ms容差
        if (std::abs((rgb_time - depth_time).toSec()) > max_time_diff ||
            std::abs((rgb_time - mask_time).toSec()) > max_time_diff ||
            std::abs((rgb_time - pose_time).toSec()) > max_time_diff) {
            return;  // 时间戳不匹配，等待下一帧
        }
        
        processFrame();
    }

    void processFrame()
    {
        // 添加频率控制
        static ros::Time last_process_time = ros::Time(0);
        ros::Time current_time = ros::Time::now();
        
        if ((current_time - last_process_time).toSec() < 0.1) {  // 限制10Hz
            return;
        }
        
        if (frame_count_ >= max_frames_) {
            ROS_INFO("Reached max_frames (%d), stopping...", max_frames_);
            saveResults();
            return;
        }
        
        last_process_time = current_time;
        
        // 添加性能监控
        ROS_INFO("Processing frame %d", frame_count_);
        
        // 原有处理逻辑...
        if ((frame_count_ - prev_frame_id_) < frame_gap_) {
            frame_count_++;
            return;
        }

        ROS_INFO("Processing frame %d...", frame_count_);
        tic_toc_seq_.tic();

        try {
            // Convert ROS images to Open3D format
            auto rgbd = convertRosToRGBD(latest_rgb_, latest_depth_);
            if (!rgbd) {
                ROS_ERROR("Failed to convert ROS images to RGBD");
                return;
            }

            // Get camera pose from pose topic
            Eigen::Matrix4d pose = getCameraPoseFromTopic();

            // Parse detections
            std::vector<DetectionPtr> detections;
            if (!parseDetections(latest_detection_json_, latest_mask_, detections)) {
                ROS_WARN("Failed to parse detections for frame %d", frame_count_);
                return;
            }

            tic_toc_seq_.toc();

            // Perform semantic mapping
            semantic_mapping_->integrate(frame_count_, rgbd, pose, detections);
            tic_toc_seq_.toc();

            // Visualization
            visualizeResults(pose, rgbd, detections);

            prev_frame_id_ = frame_count_;
            frame_count_++;

            tic_toc_seq_.toc();
            ROS_INFO("Frame %d processed successfully", frame_count_ - 1);

            // Clear data to wait for next synchronized set
            latest_rgb_.reset();
            latest_depth_.reset();
            latest_detection_json_.clear();
            latest_mask_.reset();
            latest_pose_.reset();

        } catch (const std::exception& e) {
            ROS_ERROR("Error processing frame %d: %s", frame_count_, e.what());
        }
    }

    std::shared_ptr<open3d::geometry::RGBDImage> convertRosToRGBD(
        const sensor_msgs::Image::ConstPtr& rgb_msg,
        const sensor_msgs::Image::ConstPtr& depth_msg)
    {
        try {
            // Convert RGB image
            cv_bridge::CvImagePtr cv_rgb = cv_bridge::toCvCopy(rgb_msg, sensor_msgs::image_encodings::BGR8);
            cv::Mat rgb_mat;
            cv::cvtColor(cv_rgb->image, rgb_mat, cv::COLOR_BGR2RGB);

            open3d::geometry::Image color;
            color.Prepare(rgb_mat.cols, rgb_mat.rows, 3, 1);
            memcpy(color.data_.data(), rgb_mat.data, rgb_mat.total() * rgb_mat.elemSize());

            // Convert depth image
            cv_bridge::CvImagePtr cv_depth = cv_bridge::toCvCopy(depth_msg, sensor_msgs::image_encodings::TYPE_16UC1);

            open3d::geometry::Image depth;
            depth.Prepare(cv_depth->image.cols, cv_depth->image.rows, 1, 2);
            memcpy(depth.data_.data(), cv_depth->image.data, cv_depth->image.total() * cv_depth->image.elemSize());

            // Create RGBD image
            auto rgbd = open3d::geometry::RGBDImage::CreateFromColorAndDepth(
                color, depth,
                global_config_->mapping_cfg.depth_scale,
                global_config_->mapping_cfg.depth_max,
                false);

            return rgbd;

        } catch (cv_bridge::Exception& e) {
            ROS_ERROR("cv_bridge exception: %s", e.what());
            return nullptr;
        }
    }

    Eigen::Matrix4d getCameraPoseFromTopic()
    {
        if (!latest_pose_) {
            ROS_WARN("No pose data available");
            return Eigen::Matrix4d::Identity();
        }

        try {
            // Convert nav_msgs::Odometry to Eigen matrix
            Eigen::Matrix4d pose = Eigen::Matrix4d::Identity();

            // Translation
            pose(0, 3) = latest_pose_->pose.pose.position.x;
            pose(1, 3) = latest_pose_->pose.pose.position.y;
            pose(2, 3) = latest_pose_->pose.pose.position.z;

            // Rotation (quaternion to rotation matrix)
            Eigen::Quaterniond q(latest_pose_->pose.pose.orientation.w,
                                latest_pose_->pose.pose.orientation.x,
                                latest_pose_->pose.pose.orientation.y,
                                latest_pose_->pose.pose.orientation.z);

            Eigen::Matrix3d rotation_matrix = q.toRotationMatrix();
            pose.block<3,3>(0,0) = rotation_matrix;

            return pose;

        } catch (const std::exception& e) {
            ROS_WARN("Failed to convert pose: %s", e.what());
            return Eigen::Matrix4d::Identity();
        }
    }

    bool parseDetections(const std::string& json_str,
                        const sensor_msgs::Image::ConstPtr& mask_msg,
                        std::vector<DetectionPtr>& detections)
    {
        try {
            Json::Value root;
            Json::Reader reader;

            if (!reader.parse(json_str, root)) {
                ROS_ERROR("Failed to parse JSON: %s", reader.getFormattedErrorMessages().c_str());
                return false;
            }

            if (!root.isMember("mask")) {
                ROS_WARN("No 'mask' field in JSON");
                return true; // Return true but with empty detections
            }

            const Json::Value& mask_array = root["mask"];

            // Convert mask image
            cv_bridge::CvImagePtr cv_mask;
            if (mask_msg->encoding == sensor_msgs::image_encodings::MONO8) {
                cv_mask = cv_bridge::toCvCopy(mask_msg, sensor_msgs::image_encodings::MONO8);
            } else {
                cv_mask = cv_bridge::toCvCopy(mask_msg, sensor_msgs::image_encodings::BGR8);
            }

            // Parse each detection
            for (int i = 0; i < mask_array.size(); ++i) {
                const Json::Value& detection_json = mask_array[i];

                if (!detection_json.isMember("value")) continue;
                int detection_id = detection_json["value"].asInt();
                if (detection_id == 0) continue; // Skip background

                auto detection = std::make_shared<Detection>(detection_id);

                // Parse labels and scores
                if (detection_json.isMember("labels")) {
                    const Json::Value& labels_obj = detection_json["labels"];
                    for (const auto& label_name : labels_obj.getMemberNames()) {
                        float score = labels_obj[label_name].asFloat();
                        detection->labels_.push_back(std::make_pair(label_name, score));
                    }
                } else if (detection_json.isMember("label")) {
                    // Fallback for single label format
                    std::string label = detection_json["label"].asString();
                    float score = 1.0f;
                    detection->labels_.push_back(std::make_pair(label, score));
                }

                // Create mask for this detection
                cv::Mat detection_mask = cv::Mat::zeros(cv_mask->image.size(), CV_8UC1);

                if (cv_mask->image.channels() == 1) {
                    // Grayscale mask - direct pixel value comparison
                    for (int v = 0; v < cv_mask->image.rows; v++) {
                        for (int u = 0; u < cv_mask->image.cols; u++) {
                            if (cv_mask->image.at<uchar>(v, u) == detection_id) {
                                detection_mask.at<uchar>(v, u) = 255;
                            }
                        }
                    }
                } else {
                    // BGR mask - use first channel
                    std::vector<cv::Mat> channels;
                    cv::split(cv_mask->image, channels);
                    cv::Mat first_channel = channels[0];

                    for (int v = 0; v < first_channel.rows; v++) {
                        for (int u = 0; u < first_channel.cols; u++) {
                            if (first_channel.at<uchar>(v, u) == detection_id) {
                                detection_mask.at<uchar>(v, u) = 255;
                            }
                        }
                    }
                }

                // Convert to detection format - instances_idxs_ is a cv::Mat
                detection->instances_idxs_ = detection_mask.clone();

                detections.push_back(detection);
            }

            ROS_INFO("Parsed %zu detections from JSON", detections.size());
            return true;

        } catch (const std::exception& e) {
            ROS_ERROR("Exception in parseDetections: %s", e.what());
            return false;
        }
    }

    void visualizeResults(const Eigen::Matrix4d& pose,
                         const std::shared_ptr<open3d::geometry::RGBDImage>& rgbd,
                         const std::vector<DetectionPtr>& detections)
    {
        // Render camera pose
        Visualization::render_camera_pose(pose, viz_.camera_pose, LOCAL_AGENT_, frame_count_);

        // Render path
        Visualization::render_path(pose, viz_.path_msg, viz_.path, LOCAL_AGENT_, frame_count_);

        // Render RGB detections if there are subscribers
        if (viz_.pred_image.getNumSubscribers() > 0) {
            Visualization::render_rgb_detections(rgbd->color_, detections, viz_.pred_image, LOCAL_AGENT_);
        }

        // Render 3D semantic map
        Visualization::render_semantic_map(
            semantic_mapping_->export_global_pcd(true, 0.05),
            semantic_mapping_->export_instance_centroids(0, debug_),
            semantic_mapping_->export_instance_annotations(0),
            viz_,
            LOCAL_AGENT_);
    }

    void saveResults()
    {
        if (!semantic_mapping_) return;

        ROS_INFO("Processing final results...");
        semantic_mapping_->extract_point_cloud();
        semantic_mapping_->merge_floor(true);

        // Final visualization
        Visualization::render_semantic_map(
            semantic_mapping_->export_global_pcd(true, 0.05),
            semantic_mapping_->export_instance_centroids(0, debug_),
            semantic_mapping_->export_instance_annotations(0),
            viz_,
            LOCAL_AGENT_);

        if (!output_folder_.empty()) {
            std::string sequence_name = "online_mapping";
            std::string full_output_path = output_folder_ + "/" + sequence_name;

            ROS_INFO("Saving results to %s", full_output_path.c_str());
            semantic_mapping_->Save(full_output_path);
            tic_toc_seq_.export_data(full_output_path + "/time_records.txt");
            utility::write_config(full_output_path + "/config.txt", *global_config_);

            ROS_INFO("Results saved successfully");
        }
    }
};

int main(int argc, char **argv)
{
    ros::init(argc, argv, "OnlineMappingNodeV2");
    ros::NodeHandle nh;
    ros::NodeHandle nh_private("~");

    try {
        OnlineMappingNodeV2 mapping_node(nh, nh_private);

        ROS_INFO("OnlineMappingNodeV2 started. Waiting for synchronized data...");

        ros::spin();

    } catch (const std::exception& e) {
        ROS_ERROR("OnlineMappingNodeV2 failed: %s", e.what());
        return -1;
    }

    return 0;
}
